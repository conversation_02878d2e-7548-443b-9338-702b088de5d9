#!/bin/bash

# 生产环境部署脚本

echo "开始部署 Pecco 宠物用品官网..."

# 检查环境变量文件（优先使用 config/env.prod）
ENV_FILE=${ENV_FILE:-config/env.prod}
if [ ! -f "$ENV_FILE" ]; then
    echo "错误: 找不到 $ENV_FILE 文件，请先配置生产环境变量"
    exit 1
fi

# 加载环境变量
set -a
. "$ENV_FILE"
set +a

# 拉取最新代码（主要为了更新 nginx.conf 等宿主机文件以及构建源代码）
echo "拉取最新代码 (main 分支)..."
BRANCH=${GIT_BRANCH:-main}
REPO_DISPLAY=${GIT_REPO:-"(本地仓库)"}
echo "将从 $REPO_DISPLAY 的 $BRANCH 分支构建"

# 检查Git状态并拉取最新代码
echo "检查Git仓库状态..."
git status --porcelain
if [ $? -ne 0 ]; then
    echo "错误: Git仓库状态异常"
    exit 1
fi

echo "拉取最新代码..."
git fetch origin "$BRANCH"
if [ $? -ne 0 ]; then
    echo "错误: 无法从远程仓库获取最新代码"
    echo "请检查网络连接和仓库访问权限"
    exit 1
fi

git reset --hard origin/"$BRANCH"
if [ $? -ne 0 ]; then
    echo "错误: 无法重置到最新代码"
    exit 1
fi

echo "代码更新成功"

# 复制环境变量文件到项目根目录
echo "复制环境变量文件..."
cp "$ENV_FILE" .env

# 停止现有容器
echo "停止现有容器..."
docker-compose -f docker-compose.prod.yml down

# 重新构建并启动
set -e

echo "展开 docker-compose 配置，确认构建上下文..."
docker-compose -f docker-compose.prod.yml config > /dev/null || { echo "docker-compose 配置错误"; exit 1; }

echo "重新构建并启动容器 (使用宿主机代码 COPY)..."
docker-compose -f docker-compose.prod.yml build --no-cache web
docker-compose -f docker-compose.prod.yml up -d

# 等待数据库启动
echo "等待数据库启动..."
sleep 30

# 执行数据库迁移
echo "执行数据库迁移..."
docker-compose -f docker-compose.prod.yml exec web python manage.py migrate

# 收集静态文件
echo "收集静态文件..."
docker-compose -f docker-compose.prod.yml exec web python manage.py collectstatic --noinput --clear --verbosity=0

echo "部署完成！"
echo "访问地址: http://124.220.186.165"
