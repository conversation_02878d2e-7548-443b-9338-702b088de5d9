{% extends "site/base.html" %}

{% block content %}
<div class="debug-container" style="padding: 20px; font-family: monospace;">
    <h1>导航调试信息</h1>
    
    <h2>当前语言: {{ locale }}</h2>
    
    <h3>会话信息</h3>
    <p>会话中的语言: {{ session_locale|default:"未设置" }}</p>
    <p>所有会话数据:</p>
    <pre>{{ all_session_data|pprint }}</pre>
    
    <h3>处理后的导航数据 (load_nav函数返回)</h3>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>Label Key</th>
            <th>Label</th>
            <th>URL</th>
            <th>Is Active</th>
        </tr>
        {% for item in nav_data %}
        <tr>
            <td>{{ item.label_key }}</td>
            <td>{{ item.label }}</td>
            <td>{{ item.url }}</td>
            <td>{{ item.is_active }}</td>
        </tr>
        {% endfor %}
    </table>
    
    <h3>数据库原始数据</h3>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>Label Key</th>
            <th>Target</th>
            <th>Type</th>
            <th>Translations</th>
        </tr>
        {% for item in raw_data %}
        <tr>
            <td>{{ item.label_key }}</td>
            <td>{{ item.target }}</td>
            <td>{{ item.type }}</td>
            <td>
                {% for locale, label in item.translations %}
                    <div><strong>{{ locale }}:</strong> {{ label }}</div>
                {% endfor %}
            </td>
        </tr>
        {% endfor %}
    </table>
    
    <h3>语言切换测试</h3>
    <p>
        <a href="/lang/en">切换到英语</a> |
        <a href="/lang/zh">切换到中文</a> |
        <a href="/lang/de">切换到德语</a> |
        <a href="/lang/fr">切换到法语</a> |
        <a href="/lang/nl">切换到荷兰语</a>
    </p>
    
    <p><a href="/">返回首页</a></p>
</div>
{% endblock %}


