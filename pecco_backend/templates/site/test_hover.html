<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Hover Effect</title>
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/site.css' %}">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
        }
        .instructions {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Product Hover Effect Test</h1>
        <p class="instructions">
            Hover over the product cards below to see the image transition effect.
            Products with hover images will smoothly transition to a different image on hover.
        </p>
        
        <div class="test-grid">
            {% for product in products %}
                <a class="product-card" href="/products/{{ product.id }}/">
                    <div class="image-container">
                        <img src="{{ product.cover_image.url }}" alt="{{ product.get_chinese_name }}" class="cover-image">
                        {% if product.hover_image %}
                            <img src="{{ product.hover_image.url }}" alt="{{ product.get_chinese_name }}" class="hover-image">
                        {% endif %}
                    </div>
                    <div class="product-info">
                        <h4>{{ product.get_chinese_name }}</h4>
                        <p>
                            {% if product.hover_image %}
                                ✅ Has hover effect
                            {% else %}
                                ❌ No hover image
                            {% endif %}
                        </p>
                    </div>
                </a>
            {% empty %}
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                    <p>No products with hover images found. Please run the test image creation script first.</p>
                </div>
            {% endfor %}
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/products/" style="color: #007bff; text-decoration: none;">← Back to Products</a>
        </div>
    </div>
</body>
</html>
