{% extends 'site/base.html' %}
{% load static %}
{% load multilang_tags %}
{% block content %}
{% for b in blocks %}
  {% if b.block_type == 'carousel' %}
  <section class="hero">
    <div class="carousel">
      {% for c in carousels %}
      <div class="slide{% if forloop.first %} active{% endif %}">
        <img src="{{ c.image }}" alt="carousel">
        <div class="overlay">
          <h2>{{ c.title }}</h2>
          <p>{{ c.subtitle }}</p>
          {% if c.cta_text %}<a class="btn" href="{{ c.link }}">{{ c.cta_text }}</a>{% endif %}
        </div>
      </div>
      {% empty %}
        <div class="slide placeholder active">{% get_block_name 'carousel' locale %}</div>
      {% endfor %}
      {% if carousels|length > 1 %}
      <div class="carousel-indicators">
        {% for c in carousels %}
        <div class="carousel-indicator{% if forloop.first %} active{% endif %}" data-slide="{{ forloop.counter0 }}"></div>
        {% endfor %}
      </div>
      {% endif %}
    </div>
  </section>
  {% elif b.block_type == 'about_us' %}
  <section class="about-us-section">
    <div class="section-header">
      <h2 class="section-title">{% get_block_name 'about_us' locale %}</h2>
      <div class="section-action"><a href="/about/" class="small-btn">{% if locale == 'zh' %}了解更多{% elif locale == 'nl' %}Meer info{% elif locale == 'fr' %}En savoir plus{% elif locale == 'de' %}Mehr erfahren{% else %}Learn More{% endif %}</a></div>
    </div>
    <div class="about-content">
      <p>{% if locale == 'zh' %}位于荷兰奈克尔克，仓库设在费嫩达尔。Pecco pets 致力于成为优质且价格合理的家居和宠物用品供应商，专注于品质、易用性和耐用性。{% elif locale == 'nl' %}Gevestigd in Nijkerk, met magazijn in Veenendaal. Pecco pets positioneert zich als leverancier van premium maar redelijk geprijsde huis- en dierenproducten, met focus op kwaliteit, gebruiksgemak en duurzaamheid.{% elif locale == 'fr' %}Situé à Nijkerk, avec entrepôt à Veenendaal. Pecco pets se positionne comme un fournisseur de produits domestiques et pour animaux de compagnie haut de gamme mais à prix raisonnable, en mettant l'accent sur la qualité, la facilité d'utilisation et la durabilité.{% elif locale == 'de' %}Mit Sitz in Nijkerk und Lager in Veenendaal. Pecco pets positioniert sich als Anbieter von hochwertigen, aber preisgünstigen Haus- und Haustierprodukten mit Fokus auf Qualität, Benutzerfreundlichkeit und Langlebigkeit.{% else %}Located in Nijkerk, with warehouse in Veenendaal. Pecco pets positions itself as a purveyor of premium yet reasonably priced home and pet products, with a focus on quality, ease of use and durability.{% endif %}</p>
    </div>
  </section>
  {% elif b.block_type == 'categories' %}
  <section class="categories">
    <div class="section-header">
      <h2 class="section-title">{% get_block_name 'categories' locale %}</h2>
      <div class="section-action"><a href="/products/" class="small-btn">{% if locale == 'zh' %}查看全部{% elif locale == 'nl' %}Bekijk alles{% elif locale == 'fr' %}Voir tout{% elif locale == 'de' %}Alle anzeigen{% else %}View All{% endif %}</a></div>
    </div>
    <div class="grid">
    {% for cat in categories %}
      <a class="category-card" href="/products/?cat={{ cat.slug }}">
        {% if cat.icon %}<img src="{{ cat.icon.url }}" alt="{{ cat.slug }}" class="avatar">{% else %}<img src="{% static 'img/pet_avatar_placeholder.png' %}" class="avatar" alt="pet"/>{% endif %}
        <div class="name">{{ cat.slug|title }}</div>
      </a>
    {% empty %}
      <p>{% if locale == 'zh' %}暂无分类{% elif locale == 'nl' %}Geen categorieën beschikbaar{% elif locale == 'fr' %}Aucune catégorie disponible{% elif locale == 'de' %}Keine Kategorien verfügbar{% else %}No categories available{% endif %}</p>
    {% endfor %}
    </div>
  </section>
  {% elif b.block_type == 'products' %}
  <section class="product-list">
    <h3>{% get_block_name 'products' locale %}</h3>
    <div class="grid">
      {% for p in featured_products %}
        <a class="product-card" href="/products/{{ p.id }}/">
          <div class="image-container">
            <img src="{{ p.cover }}" alt="{{ p.name }}" loading="lazy" class="cover-image">
            {% if p.hover %}
              <img src="{{ p.hover }}" alt="{{ p.name }}" loading="lazy" class="hover-image">
            {% endif %}
          </div>
          <div class="product-info">
            <h4>{{ p.name }}</h4>
            <p>{{ p.short_desc }}</p>
            <div class="tags">
              {% for t in p.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
            </div>
          </div>
        </a>
      {% empty %}
        <p>{% if locale == 'zh' %}暂无产品{% elif locale == 'nl' %}Geen producten{% elif locale == 'fr' %}Aucun produit{% elif locale == 'de' %}Keine Produkte{% else %}No products{% endif %}</p>
      {% endfor %}
    </div>
  </section>
  {% elif b.block_type == 'reseller' %}
  <section class="reseller-section">
    <div class="section-header">
      <div class="section-action"><a href="/contact/">{% get_block_name 'reseller' locale %}</a></div>
      <p class="section-subtitle">{% if locale == 'zh' %}加入我们的经销商网络，获得更多商业机会{% elif locale == 'nl' %}Word lid van ons dealernetwerk en ontgrendel nieuwe zakelijke kansen{% elif locale == 'fr' %}Rejoignez notre réseau de revendeurs et débloquez de nouvelles opportunités commerciales{% elif locale == 'de' %}Werden Sie Teil unseres Händlernetzwerks und erschließen Sie neue Geschäftsmöglichkeiten{% else %}Join our reseller network and unlock new business opportunities{% endif %}</p>
    </div>
  </section>

  {% endif %}
{% endfor %}
{% endblock %}

