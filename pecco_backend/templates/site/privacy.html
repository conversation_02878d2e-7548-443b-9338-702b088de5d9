{% extends 'site/base.html' %}
{% load static %}
{% load multilang_tags %}

{% block content %}
<div class="privacy-page">
  <div class="container">
    <div class="privacy-header">
      <h1 class="privacy-title">{% get_privacy_text 'title' locale %}</h1>
      <p class="privacy-subtitle">{% get_privacy_text 'subtitle' locale %}</p>
    </div>
    
    <div class="privacy-content">
      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'introduction' locale %}</h2>
        <p>{% get_privacy_text 'introduction_content' locale %}</p>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'information_collect' locale %}</h2>
        
        <div class="info-category">
          <h3 class="category-title">{% get_privacy_text 'voluntary_info' locale %}</h3>
          <p>{% get_privacy_text 'voluntary_info_desc' locale %}</p>
          <ul class="info-list">
            <li>{% get_privacy_text 'voluntary_item_1' locale %}</li>
            <li>{% get_privacy_text 'voluntary_item_2' locale %}</li>
            <li>{% get_privacy_text 'voluntary_item_3' locale %}</li>
            <li>{% get_privacy_text 'voluntary_item_4' locale %}</li>
          </ul>
        </div>

        <div class="info-category">
          <h3 class="category-title">{% get_privacy_text 'automatic_info' locale %}</h3>
          <p>{% get_privacy_text 'automatic_info_desc' locale %}</p>
          <ul class="info-list">
            <li>{% get_privacy_text 'automatic_item_1' locale %}</li>
            <li>{% get_privacy_text 'automatic_item_2' locale %}</li>
            <li>{% get_privacy_text 'automatic_item_3' locale %}</li>
            <li>{% get_privacy_text 'automatic_item_4' locale %}</li>
          </ul>
        </div>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'how_use_info' locale %}</h2>
        <p>{% get_privacy_text 'how_use_info_desc' locale %}</p>
        <ul class="info-list">
          <li>{% get_privacy_text 'use_item_1' locale %}</li>
          <li>{% get_privacy_text 'use_item_2' locale %}</li>
          <li>{% get_privacy_text 'use_item_3' locale %}</li>
          <li>{% get_privacy_text 'use_item_4' locale %}</li>
          <li>{% get_privacy_text 'use_item_5' locale %}</li>
          <li>{% get_privacy_text 'use_item_6' locale %}</li>
        </ul>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'info_sharing' locale %}</h2>
        <ul class="info-list">
          <li>{% get_privacy_text 'sharing_item_1' locale %}</li>
          <li>{% get_privacy_text 'sharing_item_2' locale %}</li>
          <li>{% get_privacy_text 'sharing_item_3' locale %}</li>
        </ul>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'data_security' locale %}</h2>
        <p>{% get_privacy_text 'data_security_content' locale %}</p>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'your_rights' locale %}</h2>
        <p>{% get_privacy_text 'your_rights_desc' locale %}</p>
        <ul class="info-list">
          <li>{% get_privacy_text 'rights_item_1' locale %}</li>
          <li>{% get_privacy_text 'rights_item_2' locale %}</li>
          <li>{% get_privacy_text 'rights_item_3' locale %}</li>
          <li>{% get_privacy_text 'rights_item_4' locale %}</li>
        </ul>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'cookies' locale %}</h2>
        <p>{% get_privacy_text 'cookies_desc' locale %}</p>
        <ul class="info-list">
          <li>{% get_privacy_text 'cookies_item_1' locale %}</li>
          <li>{% get_privacy_text 'cookies_item_2' locale %}</li>
          <li>{% get_privacy_text 'cookies_item_3' locale %}</li>
          <li>{% get_privacy_text 'cookies_item_4' locale %}</li>
        </ul>
        <p>{% get_privacy_text 'cookies_manage' locale %}</p>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'data_retention' locale %}</h2>
        <p>{% get_privacy_text 'data_retention_desc' locale %}</p>
        <ul class="info-list">
          <li>{% get_privacy_text 'retention_item_1' locale %}</li>
          <li>{% get_privacy_text 'retention_item_2' locale %}</li>
          <li>{% get_privacy_text 'retention_item_3' locale %}</li>
        </ul>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'children_privacy' locale %}</h2>
        <p>{% get_privacy_text 'children_privacy_content' locale %}</p>
      </section>

      <section class="privacy-section">
        <h2 class="section-title">{% get_privacy_text 'policy_updates' locale %}</h2>
        <p>{% get_privacy_text 'policy_updates_content' locale %}</p>
      </section>
    </div>
  </div>
</div>

<style>
.privacy-page {
  padding: var(--spacing-large) 0;
  background: var(--color-background-light);
  min-height: 100vh;
}

.privacy-page .container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--color-background);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.privacy-header {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-hover) 100%);
  color: white;
  padding: var(--spacing-large) var(--spacing-medium);
  text-align: center;
}

.privacy-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 16px 0;
  letter-spacing: -0.02em;
}

.privacy-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.privacy-content {
  padding: var(--spacing-large) var(--spacing-medium);
}

.privacy-section {
  margin-bottom: var(--spacing-large);
}

.privacy-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-accent);
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid #e6e6e6;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--color-accent);
}

.info-category {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--color-accent);
}

.category-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-accent);
  margin: 0 0 12px 0;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 16px 0;
}

.info-list li {
  position: relative;
  padding: 8px 0 8px 24px;
  line-height: 1.6;
}

.info-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-accent);
  font-weight: bold;
  font-size: 1.2em;
}

.privacy-section p {
  margin: 16px 0;
  line-height: 1.7;
  color: var(--color-text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .privacy-page .container {
    margin: 0 16px;
    border-radius: 12px;
  }
  
  .privacy-header {
    padding: var(--spacing-medium) var(--spacing-small);
  }
  
  .privacy-title {
    font-size: 2rem;
  }
  
  .privacy-content {
    padding: var(--spacing-medium) var(--spacing-small);
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .info-category {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .privacy-title {
    font-size: 1.8rem;
  }
  
  .privacy-content {
    padding: var(--spacing-small);
  }
  
  .info-list li {
    padding-left: 20px;
  }
}
</style>
{% endblock %}
