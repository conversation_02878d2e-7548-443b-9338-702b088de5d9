{% extends 'site/base.html' %}
{% load multilang_tags %}
{% block content %}
<section class="contact">
  <div class="container">
    <div class="section-header">
      <h1 class="section-title">{% get_nav_label 'contact' locale %}</h1>
      <p class="section-subtitle">
        {% if locale == 'zh' %}
          我们很乐意听到您的声音。请填写下面的表格，我们会尽快回复您。
        {% elif locale == 'nl' %}
          We horen graag van u. Vul het onderstaande formulier in en we nemen zo snel mogelijk contact met u op.
        {% elif locale == 'fr' %}
          Nous aimerions avoir de vos nouvelles. Remplissez le formulaire ci-dessous et nous vous répondrons dès que possible.
        {% elif locale == 'de' %}
          Wir freuen uns von Ihnen zu hören. Füllen Sie das untenstehende Formular aus und wir melden uns so schnell wie möglich bei Ihnen.
        {% else %}
          We'd love to hear from you. Fill out the form below and we'll get back to you as soon as possible.
        {% endif %}
      </p>
    </div>

    <div class="contact-content">
      <div class="contact-info">
        <div class="info-item">
          <h3>{% if locale == 'zh' %}公司信息{% elif locale == 'nl' %}Bedrijfsinformatie{% elif locale == 'fr' %}Informations sur l'entreprise{% elif locale == 'de' %}Unternehmensinformationen{% else %}Company Information{% endif %}</h3>
          <div class="info-details">
            <div class="info-row">
              <span class="info-label">{% if locale == 'zh' %}公司名称{% elif locale == 'nl' %}Bedrijfsnaam{% elif locale == 'fr' %}Nom de l'entreprise{% elif locale == 'de' %}Firmenname{% else %}Company Name{% endif %}</span>
              <span class="info-value">Pecco Pets</span>
            </div>
            <div class="info-row">
              <span class="info-label">{% if locale == 'zh' %}邮箱{% elif locale == 'nl' %}E-mail{% elif locale == 'fr' %}E-mail{% elif locale == 'de' %}E-Mail{% else %}Email{% endif %}</span>
              <span class="info-value"><EMAIL></span>
            </div>
            <div class="info-row">
              <span class="info-label">{% if locale == 'zh' %}地址{% elif locale == 'nl' %}Adres{% elif locale == 'fr' %}Adresse{% elif locale == 'de' %}Adresse{% else %}Address{% endif %}</span>
              <span class="info-value">
                Douwencamp 42<br>
                3861LE Nijkerk<br>
                Netherlands
              </span>
            </div>
            <div class="info-row">
              <span class="info-label">{% if locale == 'zh' %}公司注册号{% elif locale == 'nl' %}KVK-nummer{% elif locale == 'fr' %}Numéro KVK{% elif locale == 'de' %}KVK-Nummer{% else %}KVK Number{% endif %}</span>
              <span class="info-value">97486078</span>
            </div>
            <div class="info-row">
              <span class="info-label">{% if locale == 'zh' %}税号{% elif locale == 'nl' %}BTW-nummer{% elif locale == 'fr' %}Numéro de TVA{% elif locale == 'de' %}USt-IdNr.{% else %}VAT Number{% endif %}</span>
              <span class="info-value">NL005273803B80</span>
            </div>
          </div>
        </div>
      </div>

      <div class="contact-form-wrapper">
        <form method="post" class="contact-form">
          {% csrf_token %}
          <div class="form-group">
            <label for="{{ form.name.id_for_label }}" class="form-label">
              {% if locale == 'zh' %}姓名{% elif locale == 'nl' %}Naam{% elif locale == 'fr' %}Nom{% elif locale == 'de' %}Name{% else %}Name{% endif %} *
            </label>
            {{ form.name }}
            {% if form.name.errors %}
              <div class="form-error">{{ form.name.errors.0 }}</div>
            {% endif %}
          </div>

          <div class="form-group">
            <label for="{{ form.email.id_for_label }}" class="form-label">
              {% if locale == 'zh' %}邮箱{% elif locale == 'nl' %}E-mail{% elif locale == 'fr' %}E-mail{% elif locale == 'de' %}E-Mail{% else %}Email{% endif %} *
            </label>
            {{ form.email }}
            {% if form.email.errors %}
              <div class="form-error">{{ form.email.errors.0 }}</div>
            {% endif %}
          </div>

          <div class="form-group">
            <label for="{{ form.message.id_for_label }}" class="form-label">
              {% if locale == 'zh' %}留言{% elif locale == 'nl' %}Bericht{% elif locale == 'fr' %}Message{% elif locale == 'de' %}Nachricht{% else %}Message{% endif %} *
            </label>
            {{ form.message }}
            {% if form.message.errors %}
              <div class="form-error">{{ form.message.errors.0 }}</div>
            {% endif %}
          </div>

          <button type="submit" class="btn btn-primary">
            {% if locale == 'zh' %}发送留言{% elif locale == 'nl' %}Bericht verzenden{% elif locale == 'fr' %}Envoyer le message{% elif locale == 'de' %}Nachricht senden{% else %}Send Message{% endif %}
          </button>
        </form>
      </div>
    </div>
  </div>
</section>
{% endblock %}

