{% extends 'site/base.html' %}
{% load multilang_tags %}
{% block content %}
<section class="product-list">
  <div class="product-header">
    <h3>{% get_nav_label 'products' locale %}</h3>

    <!-- Category Filter -->
    <div class="category-filter">
      <div class="filter-tabs">
        <a href="/products/" class="filter-tab{% if not current_category %} active{% endif %}">
          {% if locale == 'zh' %}全部{% elif locale == 'nl' %}Alle{% elif locale == 'fr' %}Tous{% elif locale == 'de' %}Alle{% else %}All{% endif %}
        </a>
        {% for category in categories %}
        <a href="/products/?cat={{ category.slug }}" class="filter-tab{% if category.is_active %} active{% endif %}">
          {% if category.icon %}
            <img src="{{ category.icon }}" alt="{{ category.name }}" class="category-icon">
          {% endif %}
          {{ category.name }}
        </a>
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Products Grid -->
  <div class="products-grid">
    {% if current_category %}
      <div class="category-info">
        <h4>{{ current_category_name }}</h4>
        <p class="category-count">
          {% if locale == 'zh' %}{{ items|length }} 个产品{% elif locale == 'nl' %}{{ items|length }} producten{% elif locale == 'fr' %}{{ items|length }} produits{% elif locale == 'de' %}{{ items|length }} Produkte{% else %}{{ items|length }} products{% endif %}
        </p>
      </div>
    {% endif %}

    <div class="grid">
      {% for p in items %}
        <a class="product-card" href="/products/{{ p.id }}/">
          <div class="image-container">
            <img src="{{ p.cover }}" alt="{{ p.name }}" loading="lazy" class="cover-image">
            {% if p.hover %}
              <img src="{{ p.hover }}" alt="{{ p.name }}" loading="lazy" class="hover-image">
            {% endif %}
          </div>
          <div class="product-info">
            <h4>{{ p.name }}</h4>
            <p>{{ p.short_desc }}</p>
            <div class="tags">
              {% for t in p.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
            </div>
          </div>
        </a>
      {% empty %}
        <div class="no-products">
          <p>
            {% if current_category %}
              {% if locale == 'zh' %}该分类下暂无产品{% elif locale == 'nl' %}Geen producten in deze categorie{% elif locale == 'fr' %}Aucun produit dans cette catégorie{% elif locale == 'de' %}Keine Produkte in dieser Kategorie{% else %}No products in this category{% endif %}
            {% else %}
              {% if locale == 'zh' %}暂无产品{% elif locale == 'nl' %}Geen producten{% elif locale == 'fr' %}Aucun produit{% elif locale == 'de' %}Keine Produkte{% else %}No products{% endif %}
            {% endif %}
          </p>
        </div>
      {% endfor %}
    </div>
  </div>
</section>
{% endblock %}

