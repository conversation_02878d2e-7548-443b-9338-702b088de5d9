#!/usr/bin/env python3
"""
创建测试图片的脚本
"""
import os
import django
from PIL import Image, ImageDraw, ImageFont
from django.core.files.base import ContentFile
from io import BytesIO

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pecco_backend.settings')
django.setup()

from pecco_site.models import Product

def create_test_image(text, color, size=(400, 300)):
    """创建一个简单的测试图片"""
    img = Image.new('RGB', size, color=color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 40)
    except:
        font = ImageFont.load_default()
    
    # 计算文本位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文本
    draw.text((x, y), text, fill='white', font=font)
    
    # 保存到内存
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    
    return ContentFile(buffer.getvalue())

def main():
    # 获取所有 featured 产品
    products = Product.objects.filter(is_active=True, tag_featured=True)
    if not products:
        print("没有找到 featured 产品")
        return

    colors = [
        (255, 100, 100),  # 红色
        (100, 255, 100),  # 绿色
        (100, 100, 255),  # 蓝色
        (255, 255, 100),  # 黄色
        (255, 100, 255),  # 紫色
        (100, 255, 255),  # 青色
        (255, 150, 100),  # 橙色
        (150, 100, 255),  # 紫蓝色
    ]

    for i, product in enumerate(products):
        # 如果已经有悬停图片，跳过
        if product.hover_image:
            print(f"产品 '{product.get_chinese_name()}' 已有悬停图片，跳过")
            continue

        print(f"为产品 '{product.get_chinese_name()}' 创建测试图片...")

        # 创建悬停图片
        color_index = i % len(colors)
        hover_image = create_test_image("HOVER", colors[color_index])

        # 保存悬停图片
        product.hover_image.save(
            f'hover_test_{product.id}.png',
            hover_image,
            save=True
        )

        print(f"成功为产品 {product.id} 添加了悬停图片")
        print(f"Cover image: {product.cover_image.url if product.cover_image else 'None'}")
        print(f"Hover image: {product.hover_image.url if product.hover_image else 'None'}")
        print("---")

if __name__ == '__main__':
    main()
