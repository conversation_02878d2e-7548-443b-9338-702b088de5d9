<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hover Effect</title>
    <style>
        .product-card {
            display: block;
            padding: 16px;
            border-radius: 20px;
            background: white;
            box-shadow: 0 12px 30px rgba(15,23,42,.08);
            text-decoration: none;
            transition: all .3s ease;
            border: 1px solid #f0f0f0;
            width: 300px;
            margin: 20px;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 50px rgba(15,23,42,.12);
            border-color: #e0e0e0;
        }

        .image-container {
            position: relative;
            width: 100%;
            height: 220px;
            border-radius: 16px;
            overflow: hidden;
        }

        .product-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.4s ease, transform .3s ease;
        }

        .product-card .hover-image {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .product-card:hover .cover-image {
            transform: scale(1.03);
        }

        .product-card:hover .hover-image {
            opacity: 1;
            transform: scale(1.03);
        }

        .product-info {
            padding: 16px 0;
        }

        .product-info h4 {
            margin: 0 0 8px;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .product-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Test Product Hover Effect</h1>
    
    <div class="product-card">
        <div class="image-container">
            <img src="/media/product_covers/xingqiubao.png" alt="Planet Bag" class="cover-image">
            <img src="/media/product_covers/hover_test_6_Sz3N0gF.png" alt="Planet Bag" class="hover-image">
        </div>
        <div class="product-info">
            <h4>Planet Bag</h4>
            <p>Stylish and portable pet carrier for comfortable travel</p>
        </div>
    </div>

    <div class="product-card">
        <div class="image-container">
            <img src="/media/product_covers/outdoor_2.jpg" alt="Gulu Bag" class="cover-image">
            <img src="/media/product_covers/hover_test_7.png" alt="Gulu Bag" class="hover-image">
        </div>
        <div class="product-info">
            <h4>Gulu Bag</h4>
            <p>Lightweight and comfortable pet backpack for short trips</p>
        </div>
    </div>
</body>
</html>
