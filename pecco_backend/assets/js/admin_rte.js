(function(){
  // Load TinyMCE from CDN
  var s = document.createElement('script');
  s.src = 'https://cdn.jsdelivr.net/npm/tinymce@6/tinymce.min.js';
  s.referrerPolicy = 'origin';
  s.onload = function(){
    tinymce.init({
      selector: 'textarea[name$="-content"], textarea[name="content"]',
      menubar: false,
      plugins: 'link lists image table code',
      toolbar: 'undo redo | bold italic underline | bullist numlist | link | alignleft aligncenter alignright | code',
      skin: 'oxide',
      content_css: 'default',
      height: 380,
      branding: false,
      convert_urls: false
    });
  };
  document.head.appendChild(s);
})();

