(function(){
  // Enhanced carousel with indicators
  const carousels = document.querySelectorAll('.carousel');
  carousels.forEach(function(carousel){
    const slides = carousel.querySelectorAll('.slide');
    const indicators = carousel.querySelectorAll('.carousel-indicator');
    let currentIndex = 0;
    let autoPlayInterval;
    const CAROUSEL_INTERVAL = 5000; // 5 seconds

    if (slides.length <= 1) return;

    function showSlide(index) {
      // Remove active class from all slides and indicators
      slides.forEach(slide => slide.classList.remove('active'));
      indicators.forEach(indicator => indicator.classList.remove('active'));

      // Add active class to current slide and indicator
      slides[index].classList.add('active');
      if (indicators[index]) {
        indicators[index].classList.add('active');
      }

      currentIndex = index;
    }

    function nextSlide() {
      const nextIndex = (currentIndex + 1) % slides.length;
      showSlide(nextIndex);
    }

    function startAutoPlay() {
      // Clear any existing interval first to prevent multiple intervals
      stopAutoPlay();
      autoPlayInterval = setInterval(nextSlide, CAROUSEL_INTERVAL);
    }

    function stopAutoPlay() {
      if (autoPlayInterval) {
        clearInterval(autoPlayInterval);
        autoPlayInterval = null;
      }
    }

    // Add click handlers to indicators
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        showSlide(index);
        // Restart auto-play with fresh timing after manual interaction
        startAutoPlay();
      });
    });

    // Pause auto-play on hover
    carousel.addEventListener('mouseenter', stopAutoPlay);
    carousel.addEventListener('mouseleave', startAutoPlay);

    // Start auto-play
    startAutoPlay();
  });

  // Mobile menu functionality
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileNav = document.querySelector('.mobile-nav');
  const mobileNavClose = document.querySelector('.mobile-nav-close');
  const body = document.body;

  function closeMobileMenu() {
    mobileMenuToggle.classList.remove('active');
    mobileNav.classList.remove('active');
    body.style.overflow = '';
  }

  function openMobileMenu() {
    mobileMenuToggle.classList.add('active');
    mobileNav.classList.add('active');
    body.style.overflow = 'hidden';
  }

  if (mobileMenuToggle && mobileNav) {
    mobileMenuToggle.addEventListener('click', function() {
      const isActive = mobileMenuToggle.classList.contains('active');
      if (isActive) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    });

    // Close menu with close button
    if (mobileNavClose) {
      mobileNavClose.addEventListener('click', closeMobileMenu);
    }

    // Close menu when clicking on a link
    const mobileNavLinks = mobileNav.querySelectorAll('a');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', closeMobileMenu);
    });

    // Close menu when clicking outside
    mobileNav.addEventListener('click', function(e) {
      if (e.target === mobileNav) {
        closeMobileMenu();
      }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileNav.classList.contains('active')) {
        closeMobileMenu();
      }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth > 768 && mobileNav.classList.contains('active')) {
        closeMobileMenu();
      }
    });
  }

  // Lazy load images: add loading=lazy for non-first images
  document.querySelectorAll('img').forEach((img, i)=>{
    if (!img.getAttribute('loading')) {
      img.setAttribute('loading', i === 0 ? 'eager' : 'lazy');
    }
    img.setAttribute('decoding','async');
  });

  // Product image hover effect - preload hover images for better performance
  function preloadHoverImages() {
    const hoverImages = document.querySelectorAll('.product-card .hover-image');
    hoverImages.forEach(img => {
      const preloadImg = new Image();
      preloadImg.src = img.src;
    });
  }

  // Initialize hover image preloading when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', preloadHoverImages);
  } else {
    preloadHoverImages();
  }
})();

