/* CSS Variables for consistent styling */
:root {
  --font-family-primary: 'Jo<PERSON>', -apple-system, BlinkMacSystemFont, 'Inter', 'Noto Sans SC', sans-serif;
  --color-text: #232323;
  --color-text-light: #3c3c3c;
  --color-background: #ffffff;
  --color-background-light: #f8f8f8;
  --color-accent: #051c42;
  --color-accent-hover: #234bbb;
  --spacing-section: 80px;
  --spacing-large: 60px;
  --spacing-medium: 40px;
  --spacing-small: 24px;
  --container-max-width: 1400px;
  --container-padding: 40px;
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  background: var(--color-background);
  color: var(--color-text);
  font-weight: 400;
  line-height: 1.6;
}

.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px var(--container-padding);
  background: rgba(255,255,255,.95);
  backdrop-filter: saturate(180%) blur(10px);
  position: sticky;
  top: 0;
  border-bottom: 1px solid #e6e6e6;
  z-index: 100;
}

.topbar .left {
  display: flex;
  align-items: center;
  gap: 36px;
  flex: 1;
}

.tagline-script {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
  margin-top: -2px;
  margin-left: 8px;
  cursor: default;
}

.script-line1,
.script-line2 {
  font-family: 'Dancing Script', 'Brush Script MT', cursive;
  color: var(--color-accent);
  font-weight: 600;
  font-size: 22px;
  letter-spacing: 0.4px;
  text-shadow: 0 1px 3px rgba(5,28,66,0.15);
  transition: all 0.3s ease;
}

.script-line1 {
  margin-bottom: -4px;
  transform: rotate(-1.5deg);
}

.script-line2 {
  margin-left: 12px;
  transform: rotate(1.2deg);
}

.tagline-script:hover .script-line1,
.tagline-script:hover .script-line2 {
  transform: rotate(0deg);
  color: var(--color-accent-hover);
  text-shadow: 0 2px 6px rgba(35,75,187,0.2);
}

.logo {
  width: 100px;
  height: 42px;
  transition: all .3s ease;
  filter: drop-shadow(0 2px 8px rgba(139,146,153,.15));
}

.logo:hover {
  transform: translateY(-1px);
  filter: drop-shadow(0 4px 12px rgba(139,146,153,.25));
}

/* Original tagline-bar styles - no longer used as tagline is now inline in topbar
.tagline-bar {
  background: var(--color-background-light);
  border-bottom: 1px solid #e6e6e6;
}

.tagline {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 16px var(--container-padding);
  color: var(--color-text-light);
  font-weight: 500;
  text-align: center;
  font-size: 16px;
}
*/

main.page {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.breadcrumb {
  max-width: var(--container-max-width);
  margin: 12px auto 0;
  padding: 0 var(--container-padding);
  color: #999999;
  font-size: 14px;
}

.breadcrumb a {
  color: var(--color-accent);
  text-decoration: none;
}

.breadcrumb .sep {
  margin: 0 8px;
  color: #d3d3d3;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-nav a {
  margin: 0;
  color: var(--color-text);
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 30px;
  transition: all .3s ease;
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.02em;
}

.main-nav a:hover {
  background: var(--color-background-light);
  color: var(--color-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(15,23,42,.08);
}

.main-nav a.active {
  background: var(--color-accent);
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(5,28,66,.15);
}

.main-nav a.active:hover {
  background: var(--color-accent-hover);
  color: #fff;
}

.right {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  justify-content: flex-end;
}

/* Search Styles */
.search-container {
  position: relative;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 240px;
  padding: 10px 16px 10px 44px;
  border: 1px solid #e6e6e6;
  border-radius: 24px;
  font-size: 14px;
  font-family: var(--font-family-primary);
  background: #fafafa;
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: var(--color-accent);
  background: #fff;
  box-shadow: 0 4px 12px rgba(5,28,66,0.1);
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  z-index: 2;
}

.search-input:focus ~ .search-icon {
  color: var(--color-accent);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  margin-top: 4px;
}

.search-results.show {
  display: block;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover,
.search-result-item.highlighted {
  background: #f8fafc;
}

.search-result-image {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 12px;
  background: #f1f5f9;
}

.search-result-content {
  flex: 1;
}

.search-result-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 4px 0;
}

.search-result-desc {
  font-size: 12px;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

.search-result-tags {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.search-result-tag {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  text-transform: uppercase;
}

.search-result-tag.tag-new {
  background: #dcfce7;
  color: #166534;
}

.search-result-tag.tag-hot {
  background: #fef3c7;
  color: #92400e;
}

.search-result-tag.tag-featured {
  background: #dbeafe;
  color: #1e40af;
}

.search-no-results {
  padding: 20px;
  text-align: center;
  color: #64748b;
  font-size: 14px;
}

/* Language Selector Styles */
.language-selector {
  position: relative;
  display: inline-block;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
  transition: all 0.3s ease;
  min-width: 80px;
  justify-content: space-between;
}

.language-toggle:hover {
  border-color: var(--color-accent);
  background: rgba(5, 28, 66, 0.02);
}

.language-toggle:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(5, 28, 66, 0.1);
}

.current-lang {
  font-weight: 600;
}

.dropdown-icon {
  transition: transform 0.3s ease;
  color: var(--color-text-light);
}

.language-selector:hover .dropdown-icon {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.3s ease;
  z-index: 1000;
  min-width: 160px;
}

.language-selector:hover .language-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  text-decoration: none;
  color: var(--color-text);
  transition: all 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.lang-option:last-child {
  border-bottom: none;
}

.lang-option:hover {
  background: var(--color-background-light);
  color: var(--color-accent);
}

.lang-option.active {
  background: #3b82f6;
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.lang-code {
  font-weight: 600;
  font-size: 14px;
  min-width: 30px;
}

.lang-name {
  font-size: 14px;
  font-weight: 500;
}

.lang-option.active .lang-code,
.lang-option.active .lang-name {
  color: white;
  font-weight: 700;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 101;
}

.mobile-menu-toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: saturate(180%) blur(10px);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav-header {
  position: absolute;
  top: 0;
  right: 0;
  padding: 20px;
  z-index: 102;
}

.mobile-nav-close {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: var(--color-text);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-nav-close:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--color-accent);
}

.mobile-nav.active {
  opacity: 1;
  visibility: visible;
}

.mobile-nav-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  padding: 100px 20px 40px;
  overflow-y: auto;
  max-height: 100vh;
  box-sizing: border-box;
}

.mobile-nav-content a {
  color: var(--color-text);
  text-decoration: none;
  font-size: 20px;
  font-weight: 500;
  padding: 12px 0;
  margin: 6px 0;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.mobile-nav-content a:hover {
  color: var(--color-accent-hover);
  border-bottom-color: var(--color-accent-hover);
}

.mobile-nav-content a.active {
  color: var(--color-accent);
  border-bottom-color: var(--color-accent);
}

.mobile-lang-switcher {
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-language-selector {
  width: 100%;
  max-width: 300px;
}

.mobile-lang-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-lang-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  text-decoration: none;
  color: var(--color-text);
  transition: all 0.2s ease;
  border: 1px solid transparent;
  border-radius: 8px;
  background: transparent;
}

.mobile-lang-option:hover {
  background: var(--color-background-light);
  border-color: var(--color-accent-hover);
  color: var(--color-accent-hover);
}

.mobile-lang-option.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.mobile-lang-code {
  font-weight: 600;
  font-size: 14px;
  min-width: 35px;
}

.mobile-lang-name {
  font-size: 14px;
  font-weight: 500;
}

.mobile-lang-option.active .mobile-lang-code,
.mobile-lang-option.active .mobile-lang-name {
  color: white;
  font-weight: 700;
}

.mobile-lang-switcher .divider {
  color: #d3d3d3;
  font-weight: 300;
}

.right .divider {
  color: #d3d3d3;
  margin: 0 4px;
}
/* Hero Section */
.hero {
  margin: 0;
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.hero .carousel {
  position: relative;
  height: 800px;
  overflow: hidden;
}

.hero .slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0;
  overflow: hidden;
  margin: 0;
  background: linear-gradient(180deg, rgba(0,0,0,.3), rgba(0,0,0,.1));
  box-shadow: none;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
}

.hero .slide.active {
  opacity: 1;
}

.hero .slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(5,28,66,.2) 0%, rgba(0,0,0,.4) 100%);
  z-index: 1;
}

.hero .slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.hero .slide .overlay {
  position: absolute;
  left: var(--container-padding);
  bottom: 80px;
  right: var(--container-padding);
  color: #fff;
  text-shadow: 0 4px 12px rgba(0,0,0,.6);
  max-width: 600px;
  z-index: 2;
}

.hero .slide .overlay h2 {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.hero .slide .overlay p {
  font-size: 20px;
  font-weight: 400;
  margin: 0 0 32px;
  opacity: 0.95;
  line-height: 1.5;
}

.hero .slide .overlay .btn {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 24px;
}

/* Carousel Indicators */
.hero .carousel-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 12px;
  z-index: 10;
}

.hero .carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero .carousel-indicator.active {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 1);
  transform: scale(1.2);
}

.hero .carousel-indicator:hover {
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.9);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 16px 32px;
  border-radius: 30px;
  background: var(--color-accent);
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.02em;
  transition: all .3s ease;
  border: 2px solid var(--color-accent);
}

.btn:hover {
  background: var(--color-accent-hover);
  border-color: var(--color-accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(5,28,66,.20);
}
/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 var(--spacing-medium);
  padding: 0;
  flex-direction: column;
  gap: var(--spacing-small);
}

.section-title {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.3;
  letter-spacing: -0.02em;
  margin: 0;
  text-align: center;
}

.section-action a {
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 700;
  font-size: 30px;
  padding: 8px 16px;
  border-radius: 20px;
  border-bottom: 2px solid var(--color-accent);
  transition: all .3s ease;
}

.section-action a:hover {
  background: var(--color-background-light);
  color: var(--color-accent-hover);
  border-bottom: 2px solid var(--color-accent-hover);
}

/* 为"Learn More"和"View All"按钮添加较小样式 */
.section-action a.small-btn {
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 600;
}

.section-subtitle {
  font-size: 18px;
  color: var(--color-text-light);
  margin: 0;
  max-width: 600px;
  text-align: center;
  line-height: 1.6;
}
/* About Us Section */
.about-us-section {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
}

.about-us-section .about-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about-us-section .about-content p {
  font-size: 18px;
  line-height: 1.6;
  color: var(--color-text-light);
  margin: 0;
}

/* Categories Section */
.categories {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
}

.categories .grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 250px));
  gap: var(--spacing-medium);
  justify-content: center;
  justify-items: center;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-medium) var(--spacing-small);
  border-radius: 20px;
  background: var(--color-background);
  text-decoration: none;
  color: var(--color-text);
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  transition: all .3s ease;
  border: 1px solid #f0f0f0;
  min-height: 280px;
  justify-content: center;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(15,23,42,.12);
  border-color: #e0e0e0;
}

.category-card .avatar {
  width: 140px;
  height: 140px;
  border-radius: 50%;
  box-shadow: 0 8px 20px rgba(15,23,42,.08);
  background: var(--color-background);
  object-fit: cover;
  border: 3px solid #f8f8f8;
}

.category-card .name {
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  text-align: center;
  color: var(--color-text);
}
/* Product List Section */
.product-list {
  padding: var(--spacing-section) 0;
  background: var(--color-background-light);
}

.product-list h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.product-list .grid,
.testimonials .grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-medium);
}

/* Product Card Styles */
.product-card {
  display: block;
  padding: var(--spacing-small);
  border-radius: 20px;
  background: var(--color-background);
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  color: var(--color-text);
  text-decoration: none;
  transition: all .3s ease;
  border: 1px solid #f0f0f0;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(15,23,42,.12);
  border-color: #e0e0e0;
}

.product-card .image-container {
  position: relative;
  width: 100%;
  height: 220px;
  border-radius: 16px;
  overflow: hidden;
}

.product-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.4s ease, transform .3s ease;
}

.product-card .hover-image {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.product-card:hover .cover-image {
  transform: scale(1.03);
}

.product-card:hover .hover-image {
  opacity: 1;
  transform: scale(1.03);
}

.product-card .product-info {
  padding: 16px 0;
}

.product-card h4,
.product-card .product-info h4 {
  margin: 16px 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  line-height: 1.4;
}

.product-card p,
.product-card .product-info p {
  margin: 0 0 12px;
  color: var(--color-text-light);
  font-size: 14px;
  line-height: 1.5;
}

.product-card .tags {
  margin-top: 12px;
}

.product-card .tag {
  display: inline-block;
  margin-right: 8px;
  margin-top: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: var(--color-background-light);
  color: var(--color-text-light);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-card .tag-featured {
  background: var(--color-accent);
  color: #fff;
}

.product-card .tag-hot {
  background: #ff4757;
  color: #fff;
}

.product-card .tag-new {
  background: #2ed573;
  color: #fff;
}
/* Stories Section */
.stories {
  padding: var(--spacing-section) 0 !important;
  background: var(--color-background-light);
}

.stories h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.story-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-medium);
}

/* Testimonials Section */
.testimonials {
  padding: var(--spacing-section) 0 !important;
  background: var(--color-background);
}

.testimonials h3 {
  font-size: 30px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.testimonials .product-card {
  padding: var(--spacing-medium);
}

.testimonials .product-card p {
  font-style: italic;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  color: var(--color-text-light);
}

/* Product Detail */
.product-detail {
  display: grid;
  grid-template-columns: 1.2fr .8fr;
  gap: var(--spacing-large);
  padding: var(--spacing-section) 0;
}

.product-detail .gallery .image-container {
  position: relative;
  width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
}

.product-detail .gallery img {
  width: 100%;
  transition: opacity 0.4s ease, transform .3s ease;
}

.product-detail .gallery .hover-image {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.product-detail .gallery:hover .cover-image {
  transform: scale(1.03);
}

.product-detail .gallery:hover .hover-image {
  opacity: 1;
  transform: scale(1.03);
}

/* Flash Messages */
.flash-area {
  position: fixed;
  top: 90px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 999;
}

.flash {
  background: #e6ffef;
  color: #147d3f;
  border: 1px solid #b7f0cc;
  padding: 16px 24px;
  border-radius: 20px;
  box-shadow: 0 12px 30px rgba(15,23,42,.08);
  font-weight: 500;
}

/* Footer */
.footer {
  padding: var(--spacing-large) var(--container-padding);
  color: var(--color-text-light);
  text-align: center;
  background: var(--color-background-light);
  border-top: 1px solid #e6e6e6;
  font-size: 14px;
}

/* Responsive Design */

/* Tablet and small desktop */
@media (max-width: 1024px) {
  :root {
    --container-padding: 30px;
    --spacing-section: 70px;
    --spacing-large: 50px;
    --spacing-medium: 35px;
  }

  .hero .carousel {
    height: 600px;
  }

  .hero .slide .overlay h2 {
    font-size: 40px;
  }

  .hero .slide .overlay p {
    font-size: 18px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }

  /* Adjust tagline font size for tablets */
  .script-line1,
  .script-line2 {
    font-size: 16px;
  }

  .topbar .left {
    gap: 20px;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  :root {
    --container-padding: 20px;
    --spacing-section: 60px;
    --spacing-large: 40px;
    --spacing-medium: 30px;
  }

  /* Show mobile menu toggle, hide desktop nav */
  .mobile-menu-toggle {
    display: flex;
  }

  .right .main-nav {
    display: none;
  }

  .right .search-container {
    display: none;
  }

  .right .language-selector {
    display: none;
  }

  .mobile-nav {
    display: block;
  }

  /* Hide tagline on mobile to save space */
  .tagline-script {
    display: none;
  }

  /* Adjust topbar for mobile */
  .topbar {
    padding: 15px var(--container-padding);
  }

  .logo {
    width: 80px;
    height: 34px;
  }

  .product-detail {
    grid-template-columns: 1fr;
  }

  .hero .carousel {
    height: 500px;
  }

  .hero .slide .overlay {
    left: var(--container-padding);
    bottom: 60px;
    right: var(--container-padding);
  }

  .hero .slide .overlay h2 {
    font-size: 32px;
    line-height: 1.2;
  }

  .hero .slide .overlay p {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 28px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  :root {
    --container-padding: 16px;
    --spacing-section: 50px;
    --spacing-large: 32px;
    --spacing-medium: 24px;
  }

  .topbar {
    padding: 12px var(--container-padding);
  }

  .logo {
    width: 70px;
    height: 30px;
  }

  .hero .carousel {
    height: 400px;
  }

  .hero .slide .overlay {
    left: var(--container-padding);
    bottom: 40px;
    right: var(--container-padding);
  }

  .hero .slide .overlay h2 {
    font-size: 28px;
    line-height: 1.2;
    margin-bottom: 12px;
  }

  .hero .slide .overlay p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 24px;
  }

  .categories .grid,
  .product-list .grid,
  .testimonials .grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .mobile-nav-content a {
    font-size: 16px;
    padding: 8px 0;
  }

  .mobile-nav-content {
    padding: 80px 16px 30px;
  }

  .mobile-lang-option {
    padding: 10px 12px;
  }

  .mobile-lang-code,
  .mobile-lang-name {
    font-size: 13px;
  }

  .mobile-lang-switcher {
    margin-top: 20px;
  }

  .tagline {
    font-size: 14px;
    padding: 12px var(--container-padding);
  }
}

/* Contact Page Styles */
.contact {
  padding: var(--spacing-section) 0;
}

.contact .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.contact .section-header {
  text-align: center;
  margin-bottom: var(--spacing-large);
}

.contact .section-title {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 16px;
  line-height: 1.2;
}

.contact .section-subtitle {
  font-size: 18px;
  color: var(--color-text-light);
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-large);
  align-items: start;
}

.contact-info {
  background: var(--color-background-light);
  border-radius: 20px;
  padding: var(--spacing-medium);
  box-shadow: 0 10px 24px rgba(15,23,42,.06);
}

.contact-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 24px;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  margin-bottom: 2px;
}

.info-value {
  font-size: 16px;
  color: var(--color-text);
  font-weight: 500;
  line-height: 1.4;
}

.contact-form-wrapper {
  background: #fff;
  border-radius: 20px;
  padding: var(--spacing-medium);
  box-shadow: 0 10px 24px rgba(15,23,42,.06);
  border: 1px solid #e6e6e6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e6e6e6;
  border-radius: 12px;
  font-size: 16px;
  font-family: var(--font-family-primary);
  color: var(--color-text);
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(5, 28, 66, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

.form-error {
  color: #dc2626;
  font-size: 14px;
  margin-top: 4px;
}

.btn-primary {
  background: var(--color-accent);
  color: #fff;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
  min-width: 160px;
}

.btn-primary:hover {
  background: var(--color-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(5, 28, 66, 0.2);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Contact Page Responsive */
@media (max-width: 768px) {
  .contact .section-title {
    font-size: 28px;
  }

  .contact .section-subtitle {
    font-size: 16px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-medium);
  }

  .contact-info,
  .contact-form-wrapper {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .contact .section-title {
    font-size: 24px;
  }

  .contact-info,
  .contact-form-wrapper {
    padding: 20px;
  }

  .form-input,
  .form-textarea {
    padding: 14px 16px;
  }

  .btn-primary {
    width: 100%;
    padding: 16px;
  }
}

/* Reseller Section */
.reseller-section {
  padding: var(--spacing-section) 0;
  background: var(--color-background);
  margin-top: var(--spacing-large);
}

.reseller-content {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 500px;
  margin: 0 auto;
}

.reseller-form {
  width: 100%;
}

.reseller-form .form-group {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
}

.reseller-form .form-input {
  flex: 1;
  max-width: 350px;
  margin: 0;
  display: block;
}

.reseller-form .btn {
  white-space: nowrap;
  margin: 0;
}

/* Reseller Section Responsive */
@media (max-width: 768px) {
  .reseller-section {
    margin-top: var(--spacing-medium);
  }
  
  .reseller-form .form-group {
    flex-direction: column;
    gap: 16px;
  }
  
  .reseller-form .form-input {
    max-width: 100%;
  }
  
  .reseller-form .btn {
    width: 100%;
  }
}



/* Footer */
.footer {
  background: var(--color-background-light);
  border-top: 1px solid #e6e6e6;
  padding: var(--spacing-large) 0 var(--spacing-medium);
}

.footer-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr;
  gap: var(--spacing-large);
  margin-bottom: var(--spacing-medium);
  align-items: start;
}

.footer-column h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0 0 20px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: var(--color-text-light);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--color-accent);
}



/* Footer Company Column */
.footer-company {
  text-align: left;
}

/* Footer Newsletter Column */
.footer-newsletter {
  text-align: center;
}

.newsletter-section {
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 12px;
  line-height: 1.2;
}

.newsletter-subtitle {
  font-size: 14px;
  color: var(--color-text-light);
  margin: 0 0 24px;
  line-height: 1.4;
}

.newsletter-form {
  margin-bottom: 16px;
}

.newsletter-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.newsletter-input {
  flex: 1;
  padding: 14px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 50px;
  font-size: 14px;
  color: var(--color-text);
  background: #fff;
  transition: all 0.3s ease;
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.newsletter-input::placeholder {
  color: #9ca3af;
}

.newsletter-btn {
  padding: 14px 32px;
  background: #3b82f6;
  color: #fff;
  border: none;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.newsletter-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.newsletter-message {
  min-height: 20px;
  font-size: 14px;
}

.success-message {
  color: #059669;
  font-weight: 500;
}

.error-message {
  color: #dc2626;
  font-weight: 500;
}



.company-name {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-accent);
  margin-bottom: 12px;
}

.company-address {
  font-size: 14px;
  color: var(--color-text-light);
  line-height: 1.5;
}

/* Footer Navigation Column */
.footer-nav {
  text-align: right;
}

.footer-nav .footer-links {
  text-align: right;
}

.footer-nav .footer-links a {
  font-size: 16px;
  font-weight: 500;
}

.contact-info {
  margin-bottom: 24px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--color-text-light);
  line-height: 1.5;
}

.contact-icon {
  font-size: 16px;
  margin-top: 2px;
}

.social-icons {
  display: flex;
  gap: 12px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--color-accent);
  color: #fff;
  border-radius: 50%;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: var(--color-accent-hover);
  transform: translateY(-2px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-medium);
  border-top: 1px solid #e6e6e6;
}

.copyright {
  font-size: 14px;
  color: var(--color-text-light);
  font-weight: 500;
}



/* Footer Responsive */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-medium);
  }

  .footer-company {
    order: 1;
    text-align: center;
  }

  .footer-newsletter {
    order: 2;
  }

  .newsletter-input-group {
    flex-direction: column;
    gap: 16px;
  }

  .newsletter-input {
    width: 100%;
  }

  .newsletter-btn {
    width: 100%;
    padding: 16px 32px;
  }

  .footer-nav {
    order: 3;
    text-align: center;
  }

  .footer-nav .footer-links {
    text-align: center;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .social-icons {
    gap: 8px;
  }

  .social-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

/* Product List Page */
.product-list {
  padding: var(--spacing-large) 0;
}

.product-header {
  margin-bottom: var(--spacing-large);
}

.product-header h3 {
  margin: 0 0 var(--spacing-medium);
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text);
  text-align: center;
}

/* Category Filter */
.category-filter {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-medium);
}

.filter-tabs {
  display: flex;
  gap: 8px;
  background: #f8fafc;
  padding: 6px;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 50px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-light);
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tab:hover {
  color: var(--color-accent);
  background: rgba(59, 130, 246, 0.1);
}

.filter-tab.active {
  background: var(--color-accent);
  color: #fff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.category-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

/* Category Info */
.category-info {
  text-align: center;
  margin-bottom: var(--spacing-medium);
}

.category-info h4 {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
}

.category-count {
  margin: 0;
  font-size: 14px;
  color: var(--color-text-light);
}

/* Products Grid */
.products-grid {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.no-products {
  text-align: center;
  padding: var(--spacing-large);
  color: var(--color-text-light);
}

.no-products p {
  font-size: 16px;
  margin: 0;
}

/* Removed duplicate product card styles - consolidated above */

/* Responsive Design */
@media (max-width: 768px) {
  .filter-tabs {
    flex-wrap: wrap;
    justify-content: center;
    gap: 6px;
    padding: 8px;
  }

  .filter-tab {
    padding: 10px 16px;
    font-size: 13px;
  }

  .category-icon {
    width: 16px;
    height: 16px;
  }

  .product-header h3 {
    font-size: 28px;
  }

  .category-info h4 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .filter-tabs {
    padding: 6px;
  }

  .filter-tab {
    padding: 8px 12px;
    font-size: 12px;
  }

  .product-header h3 {
    font-size: 24px;
  }

  .product-info {
    padding: 16px;
  }

  .product-info h4 {
    font-size: 16px;
  }
}

/* About Page Styles */
.about-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-large) var(--container-padding);
  line-height: 1.6;
}

.about-content h2 {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 var(--spacing-medium);
  text-align: center;
}

.about-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
  margin: var(--spacing-large) 0 var(--spacing-medium);
  border-bottom: 2px solid var(--color-accent);
  padding-bottom: 8px;
}

.about-content p {
  font-size: 16px;
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-medium);
  text-align: justify;
}

.about-content ul {
  margin: 0 0 var(--spacing-medium);
  padding-left: 0;
  list-style: none;
}

.about-content li {
  font-size: 16px;
  color: var(--color-text-light);
  margin: 0 0 var(--spacing-small);
  padding-left: 24px;
  position: relative;
}

.about-content li:before {
  content: "•";
  color: var(--color-accent);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.about-content strong {
  color: var(--color-text);
  font-weight: 600;
}

.about-content a {
  color: var(--color-accent);
  text-decoration: none;
  font-weight: 500;
}

.about-content a:hover {
  text-decoration: underline;
}

.company-intro {
  text-align: center;
  margin-bottom: var(--spacing-large);
  padding: var(--spacing-large);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
}

.company-intro p {
  font-size: 18px;
  font-weight: 500;
  color: var(--color-text);
  margin: 0;
}

.our-mission,
.our-values,
.contact-info {
  margin-bottom: var(--spacing-large);
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
  .about-content {
    padding: var(--spacing-medium) var(--container-padding);
  }

  .about-content h2 {
    font-size: 28px;
  }

  .about-content h3 {
    font-size: 20px;
  }

  .about-content p,
  .about-content li {
    font-size: 15px;
  }

  .company-intro {
    padding: var(--spacing-medium);
  }

  .company-intro p {
    font-size: 16px;
  }
}
