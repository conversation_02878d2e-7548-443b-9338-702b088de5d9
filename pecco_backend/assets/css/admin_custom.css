/* 自定义Django Admin样式 - 改善CKEditor显示 */

/* 让CKEditor在tabular inline中显示更宽 */
.inline-group .form-row .cke,
.inline-group .form-row .django-ckeditor-widget,
.tabular .form-row .cke,
.tabular .form-row .django-ckeditor-widget {
    width: 100% !important;
    min-width: 700px !important;
}

/* 调整CKEditor编辑器的最小宽度 */
.cke_editor {
    width: 100% !important;
    min-width: 700px !important;
}

/* 调整CKEditor内容区域的宽度 */
.cke_contents {
    width: 100% !important;
    min-width: 700px !important;
}

/* 强制调整CKEditor textarea的宽度 */
.django-ckeditor-widget textarea {
    width: 100% !important;
    min-width: 700px !important;
}

/* 让包含CKEditor的表格单元格更宽 */
.inline-group .form-row td.field-content,
.tabular .form-row td.field-content,
.inline-group .form-row .field-content,
.tabular .form-row .field-content {
    width: 65% !important;
    min-width: 700px !important;
    max-width: none !important;
}

/* 调整整个inline表格的布局 */
.inline-group .tabular tr.form-row td {
    vertical-align: top;
}

/* 让content字段的列更宽 */
.inline-group .tabular .field-content {
    width: 60% !important;
}

/* 调整其他字段的宽度以适应新布局 */
.inline-group .tabular .field-locale {
    width: 8% !important;
}

.inline-group .tabular .field-title {
    width: 15% !important;
}

.inline-group .tabular .field-meta_title {
    width: 8% !important;
}

.inline-group .tabular .field-meta_description {
    width: 9% !important;
}

/* 确保CKEditor工具栏正常显示 */
.cke_toolbar {
    width: 100% !important;
}

/* 改善CKEditor在小屏幕上的显示 */
@media (max-width: 1200px) {
    .inline-group .form-row .cke {
        min-width: 500px !important;
    }
    
    .cke_editor {
        min-width: 500px !important;
    }
    
    .cke_contents {
        min-width: 500px !important;
    }
    
    .inline-group .form-row td.field-content {
        min-width: 500px !important;
    }
}

/* 让整个admin页面在需要时可以水平滚动 */
.change-form .module {
    overflow-x: auto;
}

/* 调整CKEditor的高度，让它更适合编辑 */
.cke_contents iframe {
    min-height: 300px !important;
}

/* 强制让整个表格更宽，以适应CKEditor */
.inline-group .tabular,
.inline-group table {
    width: 100% !important;
    min-width: 1200px !important;
    table-layout: auto !important;
}

/* 确保content列有足够的空间 */
.inline-group .tabular th.column-content,
.inline-group .tabular td.field-content {
    width: 65% !important;
    min-width: 700px !important;
}

/* 调整其他列的宽度 */
.inline-group .tabular th.column-locale,
.inline-group .tabular td.field-locale {
    width: 8% !important;
    min-width: 80px !important;
}

.inline-group .tabular th.column-title,
.inline-group .tabular td.field-title {
    width: 12% !important;
    min-width: 120px !important;
}

.inline-group .tabular th.column-meta_title,
.inline-group .tabular td.field-meta_title {
    width: 8% !important;
    min-width: 80px !important;
}

.inline-group .tabular th.column-meta_description,
.inline-group .tabular td.field-meta_description {
    width: 7% !important;
    min-width: 70px !important;
}

/* 让admin页面可以水平滚动 */
.change-form {
    overflow-x: auto !important;
}

/* 确保CKEditor工具栏不被压缩 */
.cke_toolbar_break {
    display: none !important;
}
