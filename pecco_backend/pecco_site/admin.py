from django.contrib import admin
from ckeditor.widgets import CKEditorWidget
from django import forms
from . import models

class TranslationInlineMixin:
    extra = 0

class CategoryTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.CategoryTranslation

@admin.register(models.Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('slug','sort_order','is_active','english_name','chinese_name','dutch_name','french_name','german_name')
    list_editable = ('sort_order','is_active')
    inlines = [CategoryTranslationInline]

    def english_name(self, obj):
        """显示英文名称"""
        trans = obj.translations.filter(locale='en').first()
        return trans.name if trans else '-'
    english_name.short_description = 'English'

    def chinese_name(self, obj):
        """显示中文名称"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.name if trans else '-'
    chinese_name.short_description = '中文'

    def dutch_name(self, obj):
        """显示荷兰语名称"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.name if trans else '-'
    dutch_name.short_description = 'Nederlands'

    def french_name(self, obj):
        """显示法语名称"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.name if trans else '-'
    french_name.short_description = 'Français'

    def german_name(self, obj):
        """显示德语名称"""
        trans = obj.translations.filter(locale='de').first()
        return trans.name if trans else '-'
    german_name.short_description = 'Deutsch'

class ProductImageInline(admin.TabularInline):
    model = models.ProductImage
    extra = 1

class ProductTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.ProductTranslation

@admin.register(models.Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('id','english_name','chinese_name','dutch_name','french_name','german_name','categories_display','sort_order','is_active','tag_new','tag_hot','tag_featured')
    list_editable = ('sort_order','is_active','tag_new','tag_hot','tag_featured')
    fields = ('categories', 'cover_image', 'hover_image', 'usage', 'sort_order', 'is_active', 'tag_new', 'tag_hot', 'tag_featured')
    inlines = [ProductImageInline, ProductTranslationInline]
    filter_horizontal = ('categories',)

    def english_name(self, obj):
        """显示英文名称"""
        trans = obj.translations.filter(locale='en').first()
        return trans.name if trans else f'Product {obj.id}'
    english_name.short_description = 'English'

    def chinese_name(self, obj):
        """显示中文名称"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.name if trans else f'Product {obj.id}'
    chinese_name.short_description = '中文'

    def dutch_name(self, obj):
        """显示荷兰语名称"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.name if trans else f'Product {obj.id}'
    dutch_name.short_description = 'Nederlands'

    def french_name(self, obj):
        """显示法语名称"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.name if trans else f'Product {obj.id}'
    french_name.short_description = 'Français'

    def german_name(self, obj):
        """显示德语名称"""
        trans = obj.translations.filter(locale='de').first()
        return trans.name if trans else f'Product {obj.id}'
    german_name.short_description = 'Deutsch'

    def categories_display(self, obj):
        """显示所有分类"""
        return ', '.join([cat.slug for cat in obj.categories.all()])
    categories_display.short_description = '分类'

class CarouselTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.CarouselTranslation

@admin.register(models.CarouselItem)
class CarouselAdmin(admin.ModelAdmin):
    list_display = ('id','sort_order','is_active','english_title','chinese_title','dutch_title','french_title','german_title')
    list_editable = ('sort_order','is_active')
    inlines = [CarouselTranslationInline]

    def english_title(self, obj):
        """显示英文标题"""
        trans = obj.translations.filter(locale='en').first()
        return trans.title if trans else '-'
    english_title.short_description = 'English Title'

    def chinese_title(self, obj):
        """显示中文标题"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.title if trans else '-'
    chinese_title.short_description = '中文标题'

    def dutch_title(self, obj):
        """显示荷兰语标题"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.title if trans else '-'
    dutch_title.short_description = 'Nederlandse Titel'

    def french_title(self, obj):
        """显示法语标题"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.title if trans else '-'
    french_title.short_description = 'Titre Français'

    def german_title(self, obj):
        """显示德语标题"""
        trans = obj.translations.filter(locale='de').first()
        return trans.title if trans else '-'
    german_title.short_description = 'Deutscher Titel'



class NavigationTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.NavigationTranslation

@admin.register(models.NavigationItem)
class NavigationItemAdmin(admin.ModelAdmin):
    list_display = ('label_key','english_label','chinese_label','dutch_label','french_label','german_label','type','target','is_active','sort_order')
    list_editable = ('type','target','is_active','sort_order')
    inlines = [NavigationTranslationInline]
    ordering = ('sort_order',)

    def english_label(self, obj):
        """显示英文标签"""
        trans = obj.translations.filter(locale='en').first()
        return trans.label if trans else '-'
    english_label.short_description = 'English'

    def chinese_label(self, obj):
        """显示中文标签"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.label if trans else '-'
    chinese_label.short_description = '中文'

    def dutch_label(self, obj):
        """显示荷兰语标签"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.label if trans else '-'
    dutch_label.short_description = 'Nederlands'

    def french_label(self, obj):
        """显示法语标签"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.label if trans else '-'
    french_label.short_description = 'Français'

    def german_label(self, obj):
        """显示德语标签"""
        trans = obj.translations.filter(locale='de').first()
        return trans.label if trans else '-'
    german_label.short_description = 'Deutsch'

class HomeLayoutBlockTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.HomeLayoutBlockTranslation

@admin.register(models.HomeLayoutBlock)
class HomeLayoutBlockAdmin(admin.ModelAdmin):
    list_display = ('block_type','english_name','chinese_name','dutch_name','french_name','german_name','is_active','sort_order')
    list_editable = ('is_active','sort_order')
    inlines = [HomeLayoutBlockTranslationInline]

    def english_name(self, obj):
        """显示英文名称"""
        trans = obj.translations.filter(locale='en').first()
        return trans.title if trans else '-'
    english_name.short_description = 'English'

    def chinese_name(self, obj):
        """显示中文名称"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.title if trans else '-'
    chinese_name.short_description = '中文'

    def dutch_name(self, obj):
        """显示荷兰语名称"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.title if trans else '-'
    dutch_name.short_description = 'Nederlands'

    def french_name(self, obj):
        """显示法语名称"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.title if trans else '-'
    french_name.short_description = 'Français'

    def german_name(self, obj):
        """显示德语名称"""
        trans = obj.translations.filter(locale='de').first()
        return trans.title if trans else '-'
    german_name.short_description = 'Deutsch'

@admin.register(models.ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    list_display = ('message_type', 'display_name', 'email', 'created_at', 'is_read', 'replied_at')
    list_filter = ('message_type', 'is_read', 'created_at')
    search_fields = ('name', 'email', 'message')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('is_read',)
    ordering = ('-created_at',)

    fieldsets = (
        ('基本信息', {
            'fields': ('message_type', 'name', 'email', 'created_at')
        }),
        ('留言内容', {
            'fields': ('message',)
        }),
        ('处理状态', {
            'fields': ('is_read', 'replied_at')
        }),
    )

    def display_name(self, obj):
        """显示姓名，如果为空则显示邮箱"""
        return obj.name if obj.name else obj.email
    display_name.short_description = '姓名/邮箱'

    def has_add_permission(self, request):
        # 不允许在后台手动添加留言
        return False

class StaticPageTranslationForm(forms.ModelForm):
    content = forms.CharField(widget=CKEditorWidget())

    class Meta:
        model = models.StaticPageTranslation
        fields = '__all__'

class StaticPageTranslationInline(admin.TabularInline, TranslationInlineMixin):
    model = models.StaticPageTranslation
    form = StaticPageTranslationForm
    fields = ('locale', 'title', 'content', 'meta_title', 'meta_description')

    class Media:
        css = {
            'all': ('css/admin_custom.css',)
        }

@admin.register(models.StaticPage)
class StaticPageAdmin(admin.ModelAdmin):
    list_display = ('page_type', 'is_active', 'english_title', 'chinese_title', 'dutch_title', 'french_title', 'german_title')
    list_editable = ('is_active',)
    inlines = [StaticPageTranslationInline]
    fieldsets = (
        ('基本信息', {
            'fields': ('page_type', 'is_active')
        }),
        ('SEO设置', {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
    )

    def english_title(self, obj):
        """显示英文标题"""
        trans = obj.translations.filter(locale='en').first()
        return trans.title if trans else '-'
    english_title.short_description = 'English Title'

    def chinese_title(self, obj):
        """显示中文标题"""
        trans = obj.translations.filter(locale='zh').first()
        return trans.title if trans else '-'
    chinese_title.short_description = '中文标题'

    def dutch_title(self, obj):
        """显示荷兰语标题"""
        trans = obj.translations.filter(locale='nl').first()
        return trans.title if trans else '-'
    dutch_title.short_description = 'Nederlandse Titel'

    def french_title(self, obj):
        """显示法语标题"""
        trans = obj.translations.filter(locale='fr').first()
        return trans.title if trans else '-'
    french_title.short_description = 'Titre Français'

    def german_title(self, obj):
        """显示德语标题"""
        trans = obj.translations.filter(locale='de').first()
        return trans.title if trans else '-'
    german_title.short_description = 'Deutscher Titel'

