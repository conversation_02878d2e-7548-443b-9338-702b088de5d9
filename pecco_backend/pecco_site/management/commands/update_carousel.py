from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from pecco_site.models import CarouselItem, CarouselTranslation
import base64

# tiny 1x1 placeholder png
PNG_BASE64 = b"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8Xw8AApMBbQDRR9YAAAAASUVORK5CYII="

class Command(BaseCommand):
    help = "更新轮播图数据，确保每个轮播图都有Learn More按钮"

    def handle(self, *args, **options):
        self.stdout.write('正在更新轮播图数据...')
        
        # 轮播图数据
        carousel_data = [
            {
                'sort_order': 0,
                'translations': {
                    'zh': {
                        'title': '欢迎来到 PECCO PETS',
                        'subtitle': '优质宠物用品，合理价格',
                        'cta_text': '查看产品'
                    },
                    'en': {
                        'title': 'Welcome to PECCO PETS',
                        'subtitle': 'Premium Pet Products, Reasonable Prices',
                        'cta_text': 'View Products'
                    },
                    'nl': {
                        'title': 'Welkom bij PECCO PETS',
                        'subtitle': 'Premium Huisdierproducten, Redelijke Prijzen',
                        'cta_text': 'Bekijk Producten'
                    },
                    'fr': {
                        'title': 'Bienvenue chez PECCO PETS',
                        'subtitle': 'Produits Premium pour Animaux, Prix Raisonnables',
                        'cta_text': 'Voir Produits'
                    },
                    'de': {
                        'title': 'Willkommen bei PECCO PETS',
                        'subtitle': 'Premium Haustierprodukte, Vernünftige Preise',
                        'cta_text': 'Produkte Ansehen'
                    }
                }
            },
            {
                'sort_order': 1,
                'translations': {
                    'zh': {
                        'title': '品质与舒适并重',
                        'subtitle': '专注于更好的材料与工艺',
                        'cta_text': '查看产品'
                    },
                    'en': {
                        'title': 'Quality Meets Comfort',
                        'subtitle': 'Focus on Better Materials and Craftsmanship',
                        'cta_text': 'View Products'
                    },
                    'nl': {
                        'title': 'Kwaliteit Ontmoet Comfort',
                        'subtitle': 'Focus op Betere Materialen en Vakmanschap',
                        'cta_text': 'Bekijk Producten'
                    },
                    'fr': {
                        'title': 'La Qualité Rencontre le Confort',
                        'subtitle': 'Focus sur de Meilleurs Matériaux et Savoir-faire',
                        'cta_text': 'Voir Produits'
                    },
                    'de': {
                        'title': 'Qualität Trifft Komfort',
                        'subtitle': 'Fokus auf Bessere Materialien und Handwerkskunst',
                        'cta_text': 'Produkte Ansehen'
                    }
                }
            },
            {
                'sort_order': 2,
                'translations': {
                    'zh': {
                        'title': '为宠物与主人带来愉悦',
                        'subtitle': '易用性与耐用性的完美结合',
                        'cta_text': '查看产品'
                    },
                    'en': {
                        'title': 'Joy for Pets and Owners',
                        'subtitle': 'Perfect Blend of Ease of Use and Durability',
                        'cta_text': 'View Products'
                    },
                    'nl': {
                        'title': 'Vreugde voor Huisdieren en Eigenaren',
                        'subtitle': 'Perfecte Mix van Gebruiksgemak en Duurzaamheid',
                        'cta_text': 'Bekijk Producten'
                    },
                    'fr': {
                        'title': 'Joie pour les Animaux et Propriétaires',
                        'subtitle': 'Mélange Parfait de Facilité d\'Utilisation et Durabilité',
                        'cta_text': 'Voir Produits'
                    },
                    'de': {
                        'title': 'Freude für Haustiere und Besitzer',
                        'subtitle': 'Perfekte Mischung aus Benutzerfreundlichkeit und Langlebigkeit',
                        'cta_text': 'Produkte Ansehen'
                    }
                }
            }
        ]
        
        for carousel_info in carousel_data:
            # 创建或获取轮播图项目
            try:
                item = CarouselItem.objects.get(sort_order=carousel_info['sort_order'])
                created = False
            except CarouselItem.DoesNotExist:
                item = CarouselItem.objects.create(
                    sort_order=carousel_info['sort_order'],
                    is_active=True
                )
                created = True
            except CarouselItem.MultipleObjectsReturned:
                # 如果有多个，使用第一个并删除其他的
                items = CarouselItem.objects.filter(sort_order=carousel_info['sort_order'])
                item = items.first()
                items.exclude(id=item.id).delete()
                created = False
            
            # 如果没有图片，添加占位符图片
            if not item.image:
                item.image.save(f"carousel_{carousel_info['sort_order']}.png", ContentFile(base64.b64decode(PNG_BASE64)))

            # 设置链接到产品页面
            item.link = '/products/'
            item.save()
            
            if created:
                self.stdout.write(f'创建了新的轮播图项目 (sort_order: {carousel_info["sort_order"]})')
            else:
                self.stdout.write(f'找到现有的轮播图项目 (sort_order: {carousel_info["sort_order"]})')
            
            # 创建或更新翻译
            for locale, translation_data in carousel_info['translations'].items():
                translation, trans_created = CarouselTranslation.objects.get_or_create(
                    item=item,
                    locale=locale,
                    defaults={
                        'title': translation_data['title'],
                        'subtitle': translation_data['subtitle'],
                        'cta_text': translation_data['cta_text']
                    }
                )
                
                if not trans_created:
                    # 更新现有翻译
                    translation.title = translation_data['title']
                    translation.subtitle = translation_data['subtitle']
                    translation.cta_text = translation_data['cta_text']
                    translation.save()
                    self.stdout.write(f'更新了 {locale} 语言的翻译')
                else:
                    self.stdout.write(f'创建了 {locale} 语言的翻译')
        
        self.stdout.write(self.style.SUCCESS('轮播图数据更新完成！'))
        self.stdout.write('现在所有轮播图都有Learn More按钮，点击后会跳转到产品页面。')
