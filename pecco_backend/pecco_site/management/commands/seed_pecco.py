from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from pecco_site.models import Category, CategoryTranslation, CarouselItem, CarouselTranslation, NavigationItem, NavigationTranslation, HomeLayoutBlock, Product, ProductTranslation, ProductImage
from django.conf import settings
import base64

# tiny 1x1 placeholder png
PNG_BASE64 = b"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8Xw8AApMBbQDRR9YAAAAASUVORK5CYII="

class Command(BaseCommand):
    help = "Seed initial pecco data (categories, carousel, navigation and homepage blocks)"

    def handle(self, *args, **options):
        # Categories: dog, cat
        for slug, zh, en in [('dog','狗狗','Dog'), ('cat','猫咪','Cat')]:
            cat, _ = Category.objects.get_or_create(slug=slug)
            if not cat.icon:
                cat.icon.save(f"{slug}.png", ContentFile(base64.b64decode(PNG_BASE64)))
            CategoryTranslation.objects.get_or_create(category=cat, locale='zh', defaults={'name': zh})
            CategoryTranslation.objects.get_or_create(category=cat, locale='en', defaults={'name': en})
        # Carousel
        item, _ = CarouselItem.objects.get_or_create(sort_order=0, defaults={'is_active': True})
        if not item.image:
            item.image.save("banner.png", ContentFile(base64.b64decode(PNG_BASE64)))
        CarouselTranslation.objects.get_or_create(item=item, locale='zh', defaults={'title':'欢迎来到 pecco','subtitle':'轻奢宠物用品','cta_text':'了解更多'})
        CarouselTranslation.objects.get_or_create(item=item, locale='en', defaults={'title':'Welcome to pecco','subtitle':'Luxury Pet Essentials','cta_text':'Learn more'})
        # Navigation
        nav_items = [
            ('products','/products/','产品类型','Products'),
            ('story','/','品牌故事','Brand Story'),
            ('about','/','关于我们','About Us'),
            ('contact','/contact/','联系我们','Contact'),
        ]
        for i, (key, target, zh, en) in enumerate(nav_items):
            nav, _ = NavigationItem.objects.get_or_create(label_key=key, defaults={'type':'link','target':target,'sort_order':i})
            NavigationTranslation.objects.get_or_create(navigation=nav, locale='zh', defaults={'label': zh})
            NavigationTranslation.objects.get_or_create(navigation=nav, locale='en', defaults={'label': en})

        # Home layout blocks
        HomeLayoutBlock.objects.get_or_create(block_type='carousel', defaults={'is_active': True, 'sort_order': 0})
        HomeLayoutBlock.objects.get_or_create(block_type='categories', defaults={'is_active': True, 'sort_order': 1})
        HomeLayoutBlock.objects.get_or_create(block_type='products', defaults={'is_active': True, 'sort_order': 2, 'config': {'tag':'featured','limit':8}})


        # Sample product
        cat = Category.objects.get(slug='dog')
        p, _ = Product.objects.get_or_create(category=cat, sort_order=0, defaults={'is_active':True})
        if not p.cover_image:
            p.cover_image.save('pcover.png', ContentFile(base64.b64decode(PNG_BASE64)))
        ProductTranslation.objects.get_or_create(product=p, locale='zh', defaults={'name':'舒适项圈','short_desc':'柔软耐用','rich_desc':'<p>为爱犬设计的轻奢项圈。</p>'})
        ProductTranslation.objects.get_or_create(product=p, locale='en', defaults={'name':'Comfort Collar','short_desc':'Soft and durable','rich_desc':'<p>Luxury collar designed for your dog.</p>'})
        ProductImage.objects.get_or_create(product=p, sort_order=0, defaults={'image':p.cover_image.name})
        p.tag_featured = True
        p.save()



        self.stdout.write(self.style.SUCCESS('Seed completed.'))

