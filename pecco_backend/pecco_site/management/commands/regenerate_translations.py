from django.core.management.base import BaseCommand
from django.db import transaction
from pecco_site.models import (
    Category, CategoryTranslation, Product, ProductTranslation,
    CarouselItem, CarouselTranslation, NavigationItem, NavigationTranslation
)


class Command(BaseCommand):
    help = '重新生成荷兰语、法语、德语翻译数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成所有翻译（删除现有翻译）',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        if force:
            self.stdout.write('强制模式：将删除现有翻译并重新生成...')
            self.clear_existing_translations()
        
        self.stdout.write('开始重新生成多语言翻译...')
        
        # 重新生成所有翻译
        self.regenerate_category_translations()
        self.regenerate_product_translations()
        self.regenerate_carousel_translations()
        self.regenerate_navigation_translations()
        
        self.stdout.write(self.style.SUCCESS('多语言翻译重新生成完成！'))

    def clear_existing_translations(self):
        """清除现有的荷兰语、法语、德语翻译"""
        self.stdout.write('清除现有翻译...')
        
        # 删除非中英文的翻译
        CategoryTranslation.objects.filter(locale__in=['nl', 'fr', 'de']).delete()
        ProductTranslation.objects.filter(locale__in=['nl', 'fr', 'de']).delete()
        CarouselTranslation.objects.filter(locale__in=['nl', 'fr', 'de']).delete()
        NavigationTranslation.objects.filter(locale__in=['nl', 'fr', 'de']).delete()
        
        self.stdout.write('现有翻译已清除')

    def regenerate_category_translations(self):
        """重新生成分类翻译"""
        self.stdout.write('正在重新生成分类翻译...')
        
        # 分类的翻译映射
        category_translations = {
            'dog': {
                'nl': {'name': 'Hond', 'description': 'Hondenproducten en accessoires'},
                'fr': {'name': 'Chien', 'description': 'Produits et accessoires pour chiens'},
                'de': {'name': 'Hund', 'description': 'Hundeprodukte und Zubehör'}
            },
            'cat': {
                'nl': {'name': 'Kat', 'description': 'Kattenproducten en accessoires'},
                'fr': {'name': 'Chat', 'description': 'Produits et accessoires pour chats'},
                'de': {'name': 'Katze', 'description': 'Katzenprodukte und Zubehör'}
            },
            'accessories': {
                'nl': {'name': 'Accessoires', 'description': 'Huisdieraccessoires en benodigdheden'},
                'fr': {'name': 'Accessoires', 'description': 'Accessoires et fournitures pour animaux'},
                'de': {'name': 'Zubehör', 'description': 'Haustierzubehör und Ausstattung'}
            },
            'food': {
                'nl': {'name': 'Voeding', 'description': 'Gezonde voeding voor uw huisdier'},
                'fr': {'name': 'Nourriture', 'description': 'Nourriture saine pour votre animal'},
                'de': {'name': 'Futter', 'description': 'Gesundes Futter für Ihr Haustier'}
            },
            'toys': {
                'nl': {'name': 'Speelgoed', 'description': 'Leuk speelgoed voor uw huisdier'},
                'fr': {'name': 'Jouets', 'description': 'Jouets amusants pour votre animal'},
                'de': {'name': 'Spielzeug', 'description': 'Spaßiges Spielzeug für Ihr Haustier'}
            },
            'health': {
                'nl': {'name': 'Gezondheid', 'description': 'Gezondheidsproducten voor uw huisdier'},
                'fr': {'name': 'Santé', 'description': 'Produits de santé pour votre animal'},
                'de': {'name': 'Gesundheit', 'description': 'Gesundheitsprodukte für Ihr Haustier'}
            },
            'grooming': {
                'nl': {'name': 'Verzorging', 'description': 'Verzorgingsproducten voor uw huisdier'},
                'fr': {'name': 'Soins', 'description': 'Produits de soins pour votre animal'},
                'de': {'name': 'Pflege', 'description': 'Pflegeprodukte für Ihr Haustier'}
            }
        }
        
        for category in Category.objects.all():
            # 获取英文翻译作为基础
            en_trans = category.translations.filter(locale='en').first()
            if not en_trans:
                # 如果没有英文翻译，创建一个
                en_trans = CategoryTranslation.objects.create(
                    category=category,
                    locale='en',
                    name=category.slug.title(),
                    description=f'Products for {category.slug}'
                )
            
            # 为每种新语言创建翻译
            for locale in ['nl', 'fr', 'de']:
                if not category.translations.filter(locale=locale).exists():
                    # 查找预定义的翻译
                    predefined = category_translations.get(category.slug, {})
                    if locale in predefined:
                        name = predefined[locale]['name']
                        description = predefined[locale]['description']
                    else:
                        # 如果没有预定义翻译，使用英文翻译作为基础
                        name = f"{en_trans.name} ({locale.upper()})"
                        description = f"{en_trans.description} ({locale.upper()})"
                    
                    CategoryTranslation.objects.create(
                        category=category,
                        locale=locale,
                        name=name,
                        description=description
                    )

    def regenerate_product_translations(self):
        """重新生成产品翻译"""
        self.stdout.write('正在重新生成产品翻译...')
        
        for product in Product.objects.all():
            # 获取英文翻译作为基础
            en_trans = product.translations.filter(locale='en').first()
            if not en_trans:
                # 如果没有英文翻译，创建一个
                en_trans = ProductTranslation.objects.create(
                    product=product,
                    locale='en',
                    name=f'Product {product.id}',
                    short_desc=f'Description for product {product.id}',
                    rich_desc=f'Detailed description for product {product.id}'
                )
            
            # 为每种新语言创建翻译
            for locale in ['nl', 'fr', 'de']:
                if not product.translations.filter(locale=locale).exists():
                    # 根据语言生成翻译
                    if locale == 'nl':
                        name = f"{en_trans.name} (NL)"
                        short_desc = f"Korte beschrijving: {en_trans.short_desc}"
                        rich_desc = f"Gedetailleerde beschrijving in het Nederlands: {en_trans.rich_desc}"
                    elif locale == 'fr':
                        name = f"{en_trans.name} (FR)"
                        short_desc = f"Description courte: {en_trans.short_desc}"
                        rich_desc = f"Description détaillée en français: {en_trans.rich_desc}"
                    elif locale == 'de':
                        name = f"{en_trans.name} (DE)"
                        short_desc = f"Kurze Beschreibung: {en_trans.short_desc}"
                        rich_desc = f"Detaillierte Beschreibung auf Deutsch: {en_trans.rich_desc}"
                    
                    ProductTranslation.objects.create(
                        product=product,
                        locale=locale,
                        name=name,
                        short_desc=short_desc,
                        rich_desc=rich_desc
                    )

    def regenerate_carousel_translations(self):
        """重新生成轮播图翻译"""
        self.stdout.write('正在重新生成轮播图翻译...')
        
        for carousel in CarouselItem.objects.all():
            # 获取英文翻译作为基础
            en_trans = carousel.translations.filter(locale='en').first()
            if not en_trans:
                # 如果没有英文翻译，创建一个
                en_trans = CarouselTranslation.objects.create(
                    item=carousel,
                    locale='en',
                    title='Welcome',
                    subtitle='Discover our products',
                    cta_text='Learn More'
                )
            
            # 为每种新语言创建翻译
            for locale in ['nl', 'fr', 'de']:
                if not carousel.translations.filter(locale=locale).exists():
                    # 根据语言生成翻译
                    if locale == 'nl':
                        title = f"{en_trans.title} (NL)"
                        subtitle = f"Ontdek onze producten"
                        cta_text = "Meer info"
                    elif locale == 'fr':
                        title = f"{en_trans.title} (FR)"
                        subtitle = f"Découvrez nos produits"
                        cta_text = "En savoir plus"
                    elif locale == 'de':
                        title = f"{en_trans.title} (DE)"
                        subtitle = f"Entdecken Sie unsere Produkte"
                        cta_text = "Mehr erfahren"
                    
                    CarouselTranslation.objects.create(
                        item=carousel,
                        locale=locale,
                        title=title,
                        subtitle=subtitle,
                        cta_text=cta_text
                    )



    def regenerate_navigation_translations(self):
        """重新生成导航翻译"""
        self.stdout.write('正在重新生成导航翻译...')
        
        # 导航项的翻译映射
        nav_translations = {
            'home': {
                'nl': 'Home',
                'fr': 'Accueil',
                'de': 'Startseite'
            },
            'about': {
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'products': {
                'nl': 'Producten',
                'fr': 'Produits',
                'de': 'Produkte'
            },
            'contact': {
                'nl': 'Contact',
                'fr': 'Contact',
                'de': 'Kontakt'
            },
            'reseller': {
                'nl': 'Word dealer',
                'fr': 'Devenir revendeur',
                'de': 'Händler werden'
            }
        }
        
        for nav_item in NavigationItem.objects.all():
            # 获取英文翻译作为基础
            en_trans = nav_item.translations.filter(locale='en').first()
            if not en_trans:
                # 如果没有英文翻译，创建一个
                en_trans = NavigationTranslation.objects.create(
                    navigation=nav_item,
                    locale='en',
                    label=nav_item.label_key.title()
                )
            
            # 为每种新语言创建翻译
            for locale in ['nl', 'fr', 'de']:
                if not nav_item.translations.filter(locale=locale).exists():
                    # 查找预定义的翻译
                    predefined = nav_translations.get(nav_item.label_key, {})
                    if locale in predefined:
                        label = predefined[locale]
                    else:
                        # 如果没有预定义翻译，使用英文翻译作为基础
                        label = f"{en_trans.label} ({locale.upper()})"
                    
                    NavigationTranslation.objects.create(
                        navigation=nav_item,
                        locale=locale,
                        label=label
                    )



