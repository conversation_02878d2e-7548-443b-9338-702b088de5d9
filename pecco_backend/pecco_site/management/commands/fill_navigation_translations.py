from django.core.management.base import BaseCommand
from pecco_site.models import NavigationItem, NavigationTranslation


class Command(BaseCommand):
    help = '为导航模块填充完整的翻译数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成所有翻译（删除现有翻译）',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        if force:
            self.stdout.write('强制模式：将删除现有翻译并重新生成...')
            NavigationTranslation.objects.all().delete()
        
        self.stdout.write('开始为导航模块填充翻译数据...')
        
        # 导航项的完整翻译映射
        nav_translations = {
            'home': {
                'zh': '首页',
                'en': 'Home',
                'nl': 'Home',
                'fr': 'Accueil',
                'de': 'Startseite'
            },
            'about': {
                'zh': '关于我们',
                'en': 'About Us',
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'products': {
                'zh': '产品',
                'en': 'Products',
                'nl': 'Producten',
                'fr': 'Produits',
                'de': 'Produkte'
            },
            'contact': {
                'zh': '联系我们',
                'en': 'Contact',
                'nl': 'Contact',
                'fr': 'Contact',
                'de': 'Kontakt'
            },
            'story': {
                'zh': '品牌故事',
                'en': 'Brand Story',
                'nl': 'Merkverhaal',
                'fr': 'Histoire de marque',
                'de': 'Markengeschichte'
            },
            'reseller': {
                'zh': '成为经销商',
                'en': 'Become a Reseller',
                'nl': 'Word dealer',
                'fr': 'Devenir revendeur',
                'de': 'Händler werden'
            },
            'privacy': {
                'zh': '隐私政策',
                'en': 'Privacy Policy',
                'nl': 'Privacybeleid',
                'fr': 'Politique de confidentialité',
                'de': 'Datenschutzrichtlinie'
            },
            'categories': {
                'zh': '产品分类',
                'en': 'Categories',
                'nl': 'Categorieën',
                'fr': 'Catégories',
                'de': 'Kategorien'
            },
            'news': {
                'zh': '新闻资讯',
                'en': 'News',
                'nl': 'Nieuws',
                'fr': 'Actualités',
                'de': 'Nachrichten'
            },
            'support': {
                'zh': '客户支持',
                'en': 'Support',
                'nl': 'Ondersteuning',
                'fr': 'Support',
                'de': 'Support'
            },
            'faq': {
                'zh': '常见问题',
                'en': 'FAQ',
                'nl': 'FAQ',
                'fr': 'FAQ',
                'de': 'FAQ'
            },
            'shipping': {
                'zh': '配送信息',
                'en': 'Shipping',
                'nl': 'Verzending',
                'fr': 'Livraison',
                'de': 'Versand'
            },
            'returns': {
                'zh': '退换货政策',
                'en': 'Returns',
                'nl': 'Retourneren',
                'fr': 'Retours',
                'de': 'Rückgabe'
            },
            'warranty': {
                'zh': '保修服务',
                'en': 'Warranty',
                'nl': 'Garantie',
                'fr': 'Garantie',
                'de': 'Garantie'
            },
            'size_guide': {
                'zh': '尺寸指南',
                'en': 'Size Guide',
                'nl': 'Maattabel',
                'fr': 'Guide des tailles',
                'de': 'Größentabelle'
            },
            'care_instructions': {
                'zh': '护理说明',
                'en': 'Care Instructions',
                'nl': 'Verzorgingsinstructies',
                'fr': 'Instructions d\'entretien',
                'de': 'Pflegeanweisungen'
            }
        }
        
        # 确保所有必要的导航项都存在
        required_nav_items = [
            ('home', '/', 'link'),
            ('about', '/about/', 'link'),
            ('products', '/products/', 'link'),
            ('contact', '/contact/', 'link'),
            ('story', '/', 'link'),
            ('reseller', '/contact/', 'link'),
            ('privacy', '/privacy/', 'link'),
        ]
        
        for label_key, target, nav_type in required_nav_items:
            nav_item, created = NavigationItem.objects.get_or_create(
                label_key=label_key,
                defaults={
                    'type': nav_type,
                    'target': target,
                    'is_active': True,
                    'sort_order': len(required_nav_items)
                }
            )
            if created:
                self.stdout.write(f'创建导航项: {label_key}')
        
        # 为每个导航项添加翻译
        for nav_item in NavigationItem.objects.all():
            self.stdout.write(f'处理导航项: {nav_item.label_key}')
            
            # 为每种语言创建翻译
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                # 检查是否已存在翻译
                existing_translation = nav_item.translations.filter(locale=locale).first()
                
                # 查找预定义的翻译
                predefined = nav_translations.get(nav_item.label_key, {})
                if locale in predefined:
                    label = predefined[locale]
                else:
                    # 如果没有预定义翻译，使用英文作为基础
                    label = f"{nav_item.label_key.replace('_', ' ').title()} ({locale.upper()})"
                
                if existing_translation:
                    # 更新现有翻译
                    existing_translation.label = label
                    existing_translation.save()
                    self.stdout.write(f'  更新 {locale.upper()}: {label}')
                else:
                    # 创建新翻译
                    NavigationTranslation.objects.create(
                        navigation=nav_item,
                        locale=locale,
                        label=label
                    )
                    self.stdout.write(f'  添加 {locale.upper()}: {label}')
        
        self.stdout.write(self.style.SUCCESS('导航模块翻译数据填充完成！'))
        
        # 显示统计信息
        total_nav_items = NavigationItem.count()
        for locale in ['zh', 'en', 'nl', 'fr', 'de']:
            count = NavigationTranslation.objects.filter(locale=locale).count()
            self.stdout.write(f'{locale.upper()}: {count}/{total_nav_items}')




