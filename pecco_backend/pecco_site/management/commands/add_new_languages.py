from django.core.management.base import BaseCommand
from pecco_site.models import (
    Category, CategoryTranslation, Product, ProductTranslation,
    CarouselItem, CarouselTranslation, NavigationItem, NavigationTranslation
)


class Command(BaseCommand):
    help = '为现有数据添加荷兰语、法语和德语翻译'

    def handle(self, *args, **options):
        self.stdout.write('开始添加新语言翻译...')
        
        # 为分类添加新语言翻译
        self.add_category_translations()
        
        # 为产品添加新语言翻译
        self.add_product_translations()
        
        # 为轮播图添加新语言翻译
        self.add_carousel_translations()

        # 为导航添加新语言翻译
        self.add_navigation_translations()
        
        self.stdout.write(self.style.SUCCESS('新语言翻译添加完成！'))

    def add_category_translations(self):
        """为分类添加新语言翻译"""
        self.stdout.write('正在为分类添加新语言翻译...')
        
        # 荷兰语翻译
        dutch_translations = {
            'dog': 'Hond',
            'cat': 'Kat',
            'accessories': 'Accessoires',
            'food': 'Voeding',
            'toys': 'Speelgoed',
            'health': 'Gezondheid',
            'grooming': 'Verzorging',
        }
        
        # 法语翻译
        french_translations = {
            'dog': 'Chien',
            'cat': 'Chat',
            'accessories': 'Accessoires',
            'food': 'Nourriture',
            'toys': 'Jouets',
            'health': 'Santé',
            'grooming': 'Soins',
        }
        
        # 德语翻译
        german_translations = {
            'dog': 'Hund',
            'cat': 'Katze',
            'accessories': 'Zubehör',
            'food': 'Futter',
            'toys': 'Spielzeug',
            'health': 'Gesundheit',
            'grooming': 'Pflege',
        }
        
        for category in Category.objects.all():
            # 添加荷兰语翻译
            if not category.translations.filter(locale='nl').exists():
                dutch_name = dutch_translations.get(category.slug, f'{category.slug} (NL)')
                CategoryTranslation.objects.create(
                    category=category,
                    locale='nl',
                    name=dutch_name,
                    description=f'Beschrijving voor {dutch_name}'
                )
            
            # 添加法语翻译
            if not category.translations.filter(locale='fr').exists():
                french_name = french_translations.get(category.slug, f'{category.slug} (FR)')
                CategoryTranslation.objects.create(
                    category=category,
                    locale='fr',
                    name=french_name,
                    description=f'Description pour {french_name}'
                )
            
            # 添加德语翻译
            if not category.translations.filter(locale='de').exists():
                german_name = german_translations.get(category.slug, f'{category.slug} (DE)')
                CategoryTranslation.objects.create(
                    category=category,
                    locale='de',
                    name=german_name,
                    description=f'Beschreibung für {german_name}'
                )

    def add_product_translations(self):
        """为产品添加新语言翻译"""
        self.stdout.write('正在为产品添加新语言翻译...')
        
        for product in Product.objects.all():
            # 获取现有翻译作为参考
            en_trans = product.translations.filter(locale='en').first()
            zh_trans = product.translations.filter(locale='zh').first()
            
            # 添加荷兰语翻译
            if not product.translations.filter(locale='nl').exists():
                name = f'{en_trans.name if en_trans else f"Product {product.id}"} (NL)'
                ProductTranslation.objects.create(
                    product=product,
                    locale='nl',
                    name=name,
                    short_desc=f'Korte beschrijving voor {name}',
                    rich_desc=f'Rijke beschrijving voor {name}'
                )
            
            # 添加法语翻译
            if not product.translations.filter(locale='fr').exists():
                name = f'{en_trans.name if en_trans else f"Product {product.id}"} (FR)'
                ProductTranslation.objects.create(
                    product=product,
                    locale='fr',
                    name=name,
                    short_desc=f'Description courte pour {name}',
                    rich_desc=f'Description riche pour {name}'
                )
            
            # 添加德语翻译
            if not product.translations.filter(locale='de').exists():
                name = f'{en_trans.name if en_trans else f"Product {product.id}"} (DE)'
                ProductTranslation.objects.create(
                    product=product,
                    locale='de',
                    name=name,
                    short_desc=f'Kurze Beschreibung für {name}',
                    rich_desc=f'Reiche Beschreibung für {name}'
                )

    def add_carousel_translations(self):
        """为轮播图添加新语言翻译"""
        self.stdout.write('正在为轮播图添加新语言翻译...')
        
        for carousel in CarouselItem.objects.all():
            # 获取现有翻译作为参考
            en_trans = carousel.translations.filter(locale='en').first()
            
            # 添加荷兰语翻译
            if not carousel.translations.filter(locale='nl').exists():
                CarouselTranslation.objects.create(
                    item=carousel,
                    locale='nl',
                    title=f'{en_trans.title if en_trans else "Welkom"} (NL)',
                    subtitle=f'{en_trans.subtitle if en_trans else "Ondertitel"} (NL)',
                    cta_text=f'{en_trans.cta_text if en_trans else "Meer info"} (NL)'
                )
            
            # 添加法语翻译
            if not carousel.translations.filter(locale='fr').exists():
                CarouselTranslation.objects.create(
                    item=carousel,
                    locale='fr',
                    title=f'{en_trans.title if en_trans else "Bienvenue"} (FR)',
                    subtitle=f'{en_trans.subtitle if en_trans else "Sous-titre"} (FR)',
                    cta_text=f'{en_trans.cta_text if en_trans else "Plus d info"} (FR)'
                )
            
            # 添加德语翻译
            if not carousel.translations.filter(locale='de').exists():
                CarouselTranslation.objects.create(
                    item=carousel,
                    locale='de',
                    title=f'{en_trans.title if en_trans else "Willkommen"} (DE)',
                    subtitle=f'{en_trans.subtitle if en_trans else "Untertitel"} (DE)',
                    cta_text=f'{en_trans.cta_text if en_trans else "Mehr Info"} (DE)'
                )



    def add_navigation_translations(self):
        """为导航添加新语言翻译"""
        self.stdout.write('正在为导航添加新语言翻译...')
        
        # 导航项的翻译
        nav_translations = {
            'home': {
                'nl': 'Home',
                'fr': 'Accueil',
                'de': 'Startseite'
            },
            'about': {
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'products': {
                'nl': 'Producten',
                'fr': 'Produits',
                'de': 'Produkte'
            },
            'contact': {
                'nl': 'Contact',
                'fr': 'Contact',
                'de': 'Kontakt'
            },
            'reseller': {
                'nl': 'Word dealer',
                'fr': 'Devenir revendeur',
                'de': 'Händler werden'
            }
        }
        
        for nav_item in NavigationItem.objects.all():
            # 添加荷兰语翻译
            if not nav_item.translations.filter(locale='nl').exists():
                label = nav_translations.get(nav_item.label_key, {}).get('nl', f'{nav_item.label_key} (NL)')
                NavigationTranslation.objects.create(
                    navigation=nav_item,
                    locale='nl',
                    label=label
                )
            
            # 添加法语翻译
            if not nav_item.translations.filter(locale='fr').exists():
                label = nav_translations.get(nav_item.label_key, {}).get('fr', f'{nav_item.label_key} (FR)')
                NavigationTranslation.objects.create(
                    navigation=nav_item,
                    locale='fr',
                    label=label
                )
            
            # 添加德语翻译
            if not nav_item.translations.filter(locale='de').exists():
                label = nav_translations.get(nav_item.label_key, {}).get('de', f'{nav_item.label_key} (DE)')
                NavigationTranslation.objects.create(
                    navigation=nav_item,
                    locale='de',
                    label=label
                )


