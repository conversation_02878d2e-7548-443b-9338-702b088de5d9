from django.core.management.base import BaseCommand
from pecco_site.models import StaticPage, StaticPageTranslation, LOCALES

class Command(BaseCommand):
    help = '初始化静态页面数据'

    def handle(self, *args, **options):
        # 创建About Us页面
        about_page, created = StaticPage.objects.get_or_create(
            page_type='about',
            defaults={
                'is_active': True,
                'meta_title': 'About Us - PECCO',
                'meta_description': 'Learn more about PECCO and our mission to provide quality pet products.'
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created About Us page'))
        else:
            self.stdout.write(self.style.WARNING('About Us page already exists'))

        # 为About Us页面创建翻译
        about_translations = {
            'en': {
                'title': 'About Us',
                'content': '''<p>Pecco crafts pet essentials that blend design with comfort, focusing on better materials and details for everyday joy for pets and their owners.</p>
<p style="margin-top:10px">We believe in simplicity, durability, and comfort as the core of every product.</p>''',
                'meta_title': 'About Us - PECCO',
                'meta_description': 'Learn more about PECCO and our mission to provide quality pet products.'
            },
            'zh': {
                'title': '关于我们',
                'content': '''<p>pecco 致力于提供兼具设计与舒适的宠物用品，我们坚持更好的材料与工艺，旨在为宠物与主人带来更愉悦的日常。</p>
<p style="margin-top:10px">我们相信：简单、耐用、舒适，是产品始终如一的核心。</p>''',
                'meta_title': '关于我们 - PECCO',
                'meta_description': '了解更多关于PECCO以及我们为宠物提供优质产品的使命。'
            },
            'nl': {
                'title': 'Over ons',
                'content': '''<p>Pecco maakt huisdierbenodigdheden die design combineren met comfort, met focus op betere materialen en details voor dagelijks plezier voor huisdieren en hun eigenaren.</p>
<p style="margin-top:10px">We geloven in eenvoud, duurzaamheid en comfort als de kern van elk product.</p>''',
                'meta_title': 'Over ons - PECCO',
                'meta_description': 'Leer meer over PECCO en onze missie om kwaliteitsproducten voor huisdieren te leveren.'
            },
            'fr': {
                'title': 'À propos',
                'content': '''<p>Pecco crée des essentiels pour animaux de compagnie qui allient design et confort, en se concentrant sur de meilleurs matériaux et détails pour le bonheur quotidien des animaux et de leurs propriétaires.</p>
<p style="margin-top:10px">Nous croyons en la simplicité, la durabilité et le confort comme cœur de chaque produit.</p>''',
                'meta_title': 'À propos - PECCO',
                'meta_description': 'En savoir plus sur PECCO et notre mission de fournir des produits de qualité pour animaux de compagnie.'
            },
            'de': {
                'title': 'Über uns',
                'content': '''<p>Pecco fertigt Haustierbedarf, der Design mit Komfort verbindet und sich auf bessere Materialien und Details für den täglichen Genuss von Haustieren und ihren Besitzern konzentriert.</p>
<p style="margin-top:10px">Wir glauben an Einfachheit, Langlebigkeit und Komfort als Kern jedes Produkts.</p>''',
                'meta_title': 'Über uns - PECCO',
                'meta_description': 'Erfahren Sie mehr über PECCO und unsere Mission, qualitativ hochwertige Haustierprodukte bereitzustellen.'
            }
        }

        for locale, locale_code in LOCALES:
            if locale in about_translations:
                trans, created = StaticPageTranslation.objects.get_or_create(
                    page=about_page,
                    locale=locale,
                    defaults=about_translations[locale]
                )
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created {locale} translation for About Us'))
                else:
                    self.stdout.write(self.style.WARNING(f'{locale} translation for About Us already exists'))

        # 创建Privacy Policy页面
        privacy_page, created = StaticPage.objects.get_or_create(
            page_type='privacy',
            defaults={
                'is_active': True,
                'meta_title': 'Privacy Policy - PECCO',
                'meta_description': 'PECCO privacy policy and data protection information.'
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created Privacy Policy page'))
        else:
            self.stdout.write(self.style.WARNING('Privacy Policy page already exists'))

        # 为Privacy Policy页面创建翻译
        privacy_translations = {
            'en': {
                'title': 'Privacy Policy',
                'content': '<p>This is the privacy policy content. Please update this with your actual privacy policy.</p>',
                'meta_title': 'Privacy Policy - PECCO',
                'meta_description': 'PECCO privacy policy and data protection information.'
            },
            'zh': {
                'title': '隐私政策',
                'content': '<p>这是隐私政策内容。请更新为您的实际隐私政策。</p>',
                'meta_title': '隐私政策 - PECCO',
                'meta_description': 'PECCO隐私政策和数据保护信息。'
            },
            'nl': {
                'title': 'Privacybeleid',
                'content': '<p>Dit is de inhoud van het privacybeleid. Werk dit bij met uw daadwerkelijke privacybeleid.</p>',
                'meta_title': 'Privacybeleid - PECCO',
                'meta_description': 'PECCO privacybeleid en gegevensbeschermingsinformatie.'
            },
            'fr': {
                'title': 'Politique de confidentialité',
                'content': '<p>Ceci est le contenu de la politique de confidentialité. Veuillez le mettre à jour avec votre politique de confidentialité réelle.</p>',
                'meta_title': 'Politique de confidentialité - PECCO',
                'meta_description': 'Politique de confidentialité PECCO et informations sur la protection des données.'
            },
            'de': {
                'title': 'Datenschutzrichtlinie',
                'content': '<p>Dies ist der Inhalt der Datenschutzrichtlinie. Bitte aktualisieren Sie dies mit Ihrer tatsächlichen Datenschutzrichtlinie.</p>',
                'meta_title': 'Datenschutzrichtlinie - PECCO',
                'meta_description': 'PECCO Datenschutzrichtlinie und Datenschutzinformationen.'
            }
        }

        for locale, locale_code in LOCALES:
            if locale in privacy_translations:
                trans, created = StaticPageTranslation.objects.get_or_create(
                    page=privacy_page,
                    locale=locale,
                    defaults=privacy_translations[locale]
                )
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created {locale} translation for Privacy Policy'))
                else:
                    self.stdout.write(self.style.WARNING(f'{locale} translation for Privacy Policy already exists'))

        self.stdout.write(self.style.SUCCESS('Static pages initialization completed!'))
