from django.core.management.base import BaseCommand
from pecco_site.models import HomeLayoutBlock, HomeLayoutBlockTranslation


class Command(BaseCommand):
    help = '为首页布局块添加多语言翻译'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成所有翻译（删除现有翻译）',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        if force:
            self.stdout.write('强制模式：将删除现有翻译并重新生成...')
            HomeLayoutBlockTranslation.objects.all().delete()
        
        self.stdout.write('开始为首页布局块添加多语言翻译...')
        
        # 模块名称的翻译映射
        block_translations = {
            'carousel': {
                'en': 'Carousel',
                'zh': '轮播',
                'nl': 'Carrousel',
                'fr': 'Carrousel',
                'de': 'Karussell'
            },
            'about_us': {
                'en': 'About Us',
                'zh': '关于我们',
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'categories': {
                'en': 'Categories',
                'zh': '分类',
                'nl': 'Categorieën',
                'fr': 'Catégories',
                'de': 'Kategorien'
            },
            'products': {
                'en': 'Products',
                'zh': '产品',
                'nl': 'Producten',
                'fr': 'Produits',
                'de': 'Produkte'
            },
            'story': {
                'en': 'Brand Story',
                'zh': '品牌故事',
                'nl': 'Merkverhaal',
                'fr': 'Histoire de marque',
                'de': 'Markengeschichte'
            },
            'testimonials': {
                'en': 'Testimonials',
                'zh': '评价',
                'nl': 'Getuigenissen',
                'fr': 'Témoignages',
                'de': 'Kundenbewertungen'
            },
            'reseller': {
                'en': 'Become a Reseller',
                'zh': '成为经销商',
                'nl': 'Word dealer',
                'fr': 'Devenir revendeur',
                'de': 'Händler werden'
            },
            'custom': {
                'en': 'Custom',
                'zh': '自定义',
                'nl': 'Aangepast',
                'fr': 'Personnalisé',
                'de': 'Benutzerdefiniert'
            }
        }
        
        # 为每个布局块添加翻译
        for block in HomeLayoutBlock.objects.all():
            self.stdout.write(f'处理布局块: {block.block_type}')
            
            # 为每种语言创建翻译
            for locale in ['en', 'zh', 'nl', 'fr', 'de']:
                if not block.translations.filter(locale=locale).exists():
                    # 查找预定义的翻译
                    predefined = block_translations.get(block.block_type, {})
                    if locale in predefined:
                        display_name = predefined[locale]
                    else:
                        # 如果没有预定义翻译，使用英文作为基础
                        display_name = f"{block.block_type.title()} ({locale.upper()})"
                    
                    HomeLayoutBlockTranslation.objects.create(
                        block=block,
                        locale=locale,
                        title=display_name
                    )
                    
                    self.stdout.write(f'  添加 {locale.upper()}: {display_name}')
        
        self.stdout.write(self.style.SUCCESS('首页布局块多语言翻译添加完成！'))
        
        # 显示统计信息
        total_blocks = HomeLayoutBlock.objects.count()
        for locale in ['en', 'zh', 'nl', 'fr', 'de']:
            count = HomeLayoutBlockTranslation.objects.filter(locale=locale).count()
            self.stdout.write(f'{locale.upper()}: {count}/{total_blocks}')


