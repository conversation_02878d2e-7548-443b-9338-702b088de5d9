from django.core.management.base import BaseCommand
from pecco_site.models import (
    Category, CategoryTranslation, Product, ProductTranslation,
    CarouselItem, CarouselTranslation, NavigationItem, NavigationTranslation,
    HomeLayoutBlock, HomeLayoutBlockTranslation
)


class Command(BaseCommand):
    help = '为所有模块填充完整的翻译数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成所有翻译（删除现有翻译）',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        if force:
            self.stdout.write('强制模式：将删除现有翻译并重新生成...')
            CategoryTranslation.objects.all().delete()
            ProductTranslation.objects.all().delete()
            CarouselTranslation.objects.all().delete()

            NavigationTranslation.objects.all().delete()
            HomeLayoutBlockTranslation.objects.all().delete()
        
        self.stdout.write('开始为所有模块填充翻译数据...')
        
        # 填充分类翻译
        self.fill_category_translations()
        
        # 填充产品翻译
        self.fill_product_translations()
        
        # 填充轮播图翻译
        self.fill_carousel_translations()
        

        
        # 填充导航翻译
        self.fill_navigation_translations()
        
        # 填充首页布局块翻译
        self.fill_home_block_translations()
        
        self.stdout.write(self.style.SUCCESS('所有模块翻译数据填充完成！'))

    def fill_category_translations(self):
        """填充分类翻译"""
        self.stdout.write('正在填充分类翻译...')
        
        category_translations = {
            'dog': {
                'zh': '狗狗用品',
                'en': 'Dog Products',
                'nl': 'Hondenproducten',
                'fr': 'Produits pour chiens',
                'de': 'Hundeprodukte'
            },
            'cat': {
                'zh': '猫咪用品',
                'en': 'Cat Products',
                'nl': 'Kattenproducten',
                'fr': 'Produits pour chats',
                'de': 'Katzenprodukte'
            }
        }
        
        for category in Category.objects.all():
            translations = category_translations.get(category.slug, {})
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                if locale in translations:
                    name = translations[locale]
                    description = f'Beschrijving voor {name}' if locale == 'nl' else \
                                f'Description pour {name}' if locale == 'fr' else \
                                f'Beschreibung für {name}' if locale == 'de' else \
                                f'Description for {name}'
                else:
                    name = f'{category.slug.title()} ({locale.upper()})'
                    description = f'Description for {name}'
                
                CategoryTranslation.objects.get_or_create(
                    category=category,
                    locale=locale,
                    defaults={'name': name, 'description': description}
                )

    def fill_product_translations(self):
        """填充产品翻译"""
        self.stdout.write('正在填充产品翻译...')
        
        for product in Product.objects.all():
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                if locale == 'zh':
                    name = '舒适项圈'
                    short_desc = '柔软耐用，为爱犬设计'
                    rich_desc = '<p>为爱犬设计的轻奢项圈，采用优质材料制作，确保舒适性和耐用性。</p>'
                elif locale == 'en':
                    name = 'Comfort Collar'
                    short_desc = 'Soft and durable, designed for your dog'
                    rich_desc = '<p>Luxury collar designed for your dog, made with premium materials for comfort and durability.</p>'
                elif locale == 'nl':
                    name = 'Comfortabele Halsband'
                    short_desc = 'Zacht en duurzaam, ontworpen voor uw hond'
                    rich_desc = '<p>Luxe halsband ontworpen voor uw hond, gemaakt van premium materialen voor comfort en duurzaamheid.</p>'
                elif locale == 'fr':
                    name = 'Collier Confort'
                    short_desc = 'Doux et durable, conçu pour votre chien'
                    rich_desc = '<p>Collier de luxe conçu pour votre chien, fabriqué avec des matériaux premium pour le confort et la durabilité.</p>'
                else:  # de
                    name = 'Komfortables Halsband'
                    short_desc = 'Weich und langlebig, für Ihren Hund entwickelt'
                    rich_desc = '<p>Luxuriöses Halsband für Ihren Hund entwickelt, aus Premium-Materialien für Komfort und Langlebigkeit.</p>'
                
                ProductTranslation.objects.get_or_create(
                    product=product,
                    locale=locale,
                    defaults={'name': name, 'short_desc': short_desc, 'rich_desc': rich_desc}
                )

    def fill_carousel_translations(self):
        """填充轮播图翻译"""
        self.stdout.write('正在填充轮播图翻译...')
        
        carousel_translations = {
            'zh': {
                'title': '欢迎来到 pecco',
                'subtitle': '轻奢宠物用品，为您的爱宠提供最好的',
                'cta_text': '了解更多'
            },
            'en': {
                'title': 'Welcome to pecco',
                'subtitle': 'Luxury pet essentials for your beloved pets',
                'cta_text': 'Learn more'
            },
            'nl': {
                'title': 'Welkom bij pecco',
                'subtitle': 'Luxe huisdierbenodigdheden voor uw geliefde huisdieren',
                'cta_text': 'Meer informatie'
            },
            'fr': {
                'title': 'Bienvenue chez pecco',
                'subtitle': 'Essentiels pour animaux de luxe pour vos animaux bien-aimés',
                'cta_text': 'En savoir plus'
            },
            'de': {
                'title': 'Willkommen bei pecco',
                'subtitle': 'Luxus-Haustierbedarf für Ihre geliebten Haustiere',
                'cta_text': 'Mehr erfahren'
            }
        }
        
        for carousel in CarouselItem.objects.all():
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                translations = carousel_translations.get(locale, {})
                CarouselTranslation.objects.get_or_create(
                    item=carousel,
                    locale=locale,
                    defaults=translations
                )



    def fill_navigation_translations(self):
        """填充导航翻译"""
        self.stdout.write('正在填充导航翻译...')
        
        nav_translations = {
            'home': {
                'zh': '首页',
                'en': 'Home',
                'nl': 'Home',
                'fr': 'Accueil',
                'de': 'Startseite'
            },
            'about': {
                'zh': '关于我们',
                'en': 'About Us',
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'products': {
                'zh': '产品',
                'en': 'Products',
                'nl': 'Producten',
                'fr': 'Produits',
                'de': 'Produkte'
            },
            'contact': {
                'zh': '联系我们',
                'en': 'Contact',
                'nl': 'Contact',
                'fr': 'Contact',
                'de': 'Kontakt'
            },

        }
        
        for nav_item in NavigationItem.objects.all():
            translations = nav_translations.get(nav_item.label_key, {})
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                if locale in translations:
                    label = translations[locale]
                else:
                    label = f"{nav_item.label_key.replace('_', ' ').title()} ({locale.upper()})"
                
                NavigationTranslation.objects.get_or_create(
                    navigation=nav_item,
                    locale=locale,
                    defaults={'label': label}
                )

    def fill_home_block_translations(self):
        """填充首页布局块翻译"""
        self.stdout.write('正在填充首页布局块翻译...')
        
        block_translations = {
            'carousel': {
                'zh': '轮播展示',
                'en': 'Carousel',
                'nl': 'Carrousel',
                'fr': 'Carrousel',
                'de': 'Karussell'
            },
            'about_us': {
                'zh': '关于我们',
                'en': 'About Us',
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'categories': {
                'zh': '产品分类',
                'en': 'Categories',
                'nl': 'Categorieën',
                'fr': 'Catégories',
                'de': 'Kategorien'
            },
            'products': {
                'zh': '精选产品',
                'en': 'Featured Products',
                'nl': 'Uitgelichte producten',
                'fr': 'Produits vedettes',
                'de': 'Ausgewählte Produkte'
            },

            'reseller': {
                'zh': '成为经销商',
                'en': 'Become a Reseller',
                'nl': 'Word dealer',
                'fr': 'Devenir revendeur',
                'de': 'Händler werden'
            }
        }
        
        content_translations = {
            'carousel': {
                'zh': '展示精选产品和品牌故事',
                'en': 'Showcase featured products and brand stories',
                'nl': 'Toon uitgelichte producten en merkverhalen',
                'fr': 'Présenter les produits vedettes et les histoires de marque',
                'de': 'Präsentieren Sie ausgewählte Produkte und Markengeschichten'
            },
            'about_us': {
                'zh': '了解我们的品牌理念和使命',
                'en': 'Learn about our brand philosophy and mission',
                'nl': 'Leer meer over onze merkfilosofie en missie',
                'fr': 'Découvrez notre philosophie de marque et notre mission',
                'de': 'Erfahren Sie mehr über unsere Markenphilosophie und Mission'
            },
            'categories': {
                'zh': '浏览我们的产品分类',
                'en': 'Browse our product categories',
                'nl': 'Bekijk onze productcategorieën',
                'fr': 'Parcourez nos catégories de produits',
                'de': 'Durchsuchen Sie unsere Produktkategorien'
            },
            'products': {
                'zh': '发现我们的精选产品系列',
                'en': 'Discover our featured product collection',
                'nl': 'Ontdek onze uitgelichte productcollectie',
                'fr': 'Découvrez notre collection de produits vedettes',
                'de': 'Entdecken Sie unsere ausgewählte Produktkollektion'
            },

            'reseller': {
                'zh': '加入我们的经销商网络',
                'en': 'Join our reseller network',
                'nl': 'Word lid van ons dealer netwerk',
                'fr': 'Rejoignez notre réseau de revendeurs',
                'de': 'Werden Sie Teil unseres Händlernetzwerks'
            }
        }
        
        for block in HomeLayoutBlock.objects.all():
            title_translations = block_translations.get(block.block_type, {})
            content_translations_dict = content_translations.get(block.block_type, {})
            
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                title = title_translations.get(locale, f"{block.block_type.replace('_', ' ').title()} ({locale.upper()})")
                content = content_translations_dict.get(locale, f"Content for {block.block_type} in {locale.upper()}")
                
                HomeLayoutBlockTranslation.objects.get_or_create(
                    block=block,
                    locale=locale,
                    defaults={'title': title, 'content': content}
                )




