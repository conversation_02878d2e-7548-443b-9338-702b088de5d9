from django.core.management.base import BaseCommand
from pecco_site.models import HomeLayoutBlock, HomeLayoutBlockTranslation


class Command(BaseCommand):
    help = '为首页布局块填充完整的翻译数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成所有翻译（删除现有翻译）',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        if force:
            self.stdout.write('强制模式：将删除现有翻译并重新生成...')
            HomeLayoutBlockTranslation.objects.all().delete()
        
        self.stdout.write('开始为首页布局块填充翻译数据...')
        
        # 首页布局块的完整翻译映射
        block_translations = {
            'carousel': {
                'zh': '轮播展示',
                'en': 'Carousel',
                'nl': 'Carrousel',
                'fr': 'Carrousel',
                'de': 'Karussell'
            },
            'about_us': {
                'zh': '关于我们',
                'en': 'About Us',
                'nl': 'Over ons',
                'fr': 'À propos',
                'de': 'Über uns'
            },
            'categories': {
                'zh': '产品分类',
                'en': 'Categories',
                'nl': 'Categorieën',
                'fr': 'Catégories',
                'de': 'Kategorien'
            },
            'products': {
                'zh': '精选产品',
                'en': 'Featured Products',
                'nl': 'Uitgelichte producten',
                'fr': 'Produits vedettes',
                'de': 'Ausgewählte Produkte'
            },
            'story': {
                'zh': '品牌故事',
                'en': 'Brand Story',
                'nl': 'Merkverhaal',
                'fr': 'Histoire de marque',
                'de': 'Markengeschichte'
            },
            'testimonials': {
                'zh': '客户评价',
                'en': 'Testimonials',
                'nl': 'Getuigenissen',
                'fr': 'Témoignages',
                'de': 'Kundenbewertungen'
            },
            'reseller': {
                'zh': '成为经销商',
                'en': 'Become a Reseller',
                'nl': 'Word dealer',
                'fr': 'Devenir revendeur',
                'de': 'Händler werden'
            },
            'custom': {
                'zh': '自定义内容',
                'en': 'Custom Content',
                'nl': 'Aangepaste inhoud',
                'fr': 'Contenu personnalisé',
                'de': 'Benutzerdefinierter Inhalt'
            }
        }
        
        # 内容翻译映射
        content_translations = {
            'carousel': {
                'zh': '展示精选产品和品牌故事',
                'en': 'Showcase featured products and brand stories',
                'nl': 'Toon uitgelichte producten en merkverhalen',
                'fr': 'Présenter les produits vedettes et les histoires de marque',
                'de': 'Präsentieren Sie ausgewählte Produkte und Markengeschichten'
            },
            'about_us': {
                'zh': '了解我们的品牌理念和使命',
                'en': 'Learn about our brand philosophy and mission',
                'nl': 'Leer meer over onze merkfilosofie en missie',
                'fr': 'Découvrez notre philosophie de marque et notre mission',
                'de': 'Erfahren Sie mehr über unsere Markenphilosophie und Mission'
            },
            'categories': {
                'zh': '浏览我们的产品分类',
                'en': 'Browse our product categories',
                'nl': 'Bekijk onze productcategorieën',
                'fr': 'Parcourez nos catégories de produits',
                'de': 'Durchsuchen Sie unsere Produktkategorien'
            },
            'products': {
                'zh': '发现我们的精选产品系列',
                'en': 'Discover our featured product collection',
                'nl': 'Ontdek onze uitgelichte productcollectie',
                'fr': 'Découvrez notre collection de produits vedettes',
                'de': 'Entdecken Sie unsere ausgewählte Produktkollektion'
            },
            'story': {
                'zh': '探索我们的品牌历史和价值观',
                'en': 'Explore our brand history and values',
                'nl': 'Verken onze merkgeschiedenis en waarden',
                'fr': 'Explorez notre histoire de marque et nos valeurs',
                'de': 'Entdecken Sie unsere Markengeschichte und Werte'
            },
            'testimonials': {
                'zh': '听听我们客户的真实评价',
                'en': 'Hear what our customers have to say',
                'nl': 'Hoor wat onze klanten te zeggen hebben',
                'fr': 'Écoutez ce que nos clients ont à dire',
                'de': 'Hören Sie, was unsere Kunden zu sagen haben'
            },
            'reseller': {
                'zh': '加入我们的经销商网络',
                'en': 'Join our reseller network',
                'nl': 'Word lid van ons dealer netwerk',
                'fr': 'Rejoignez notre réseau de revendeurs',
                'de': 'Werden Sie Teil unseres Händlernetzwerks'
            },
            'custom': {
                'zh': '自定义内容区域',
                'en': 'Custom content area',
                'nl': 'Aangepast inhoudsgebied',
                'fr': 'Zone de contenu personnalisé',
                'de': 'Benutzerdefinierter Inhaltsbereich'
            }
        }
        
        # 为每个布局块添加翻译
        for block in HomeLayoutBlock.objects.all():
            self.stdout.write(f'处理布局块: {block.block_type}')
            
            # 为每种语言创建翻译
            for locale in ['zh', 'en', 'nl', 'fr', 'de']:
                # 检查是否已存在翻译
                existing_translation = block.translations.filter(locale=locale).first()
                
                # 查找预定义的标题翻译
                title_translations = block_translations.get(block.block_type, {})
                if locale in title_translations:
                    title = title_translations[locale]
                else:
                    # 如果没有预定义翻译，使用英文作为基础
                    title = f"{block.block_type.replace('_', ' ').title()} ({locale.upper()})"
                
                # 查找预定义的内容翻译
                content_translations_dict = content_translations.get(block.block_type, {})
                if locale in content_translations_dict:
                    content = content_translations_dict[locale]
                else:
                    # 如果没有预定义内容，使用默认内容
                    content = f"Content for {block.block_type} in {locale.upper()}"
                
                if existing_translation:
                    # 更新现有翻译
                    existing_translation.title = title
                    existing_translation.content = content
                    existing_translation.save()
                    self.stdout.write(f'  更新 {locale.upper()}: {title}')
                else:
                    # 创建新翻译
                    HomeLayoutBlockTranslation.objects.create(
                        block=block,
                        locale=locale,
                        title=title,
                        content=content
                    )
                    self.stdout.write(f'  添加 {locale.upper()}: {title}')
        
        self.stdout.write(self.style.SUCCESS('首页布局块翻译数据填充完成！'))
        
        # 显示统计信息
        total_blocks = HomeLayoutBlock.objects.count()
        for locale in ['zh', 'en', 'nl', 'fr', 'de']:
            count = HomeLayoutBlockTranslation.objects.filter(locale=locale).count()
            self.stdout.write(f'{locale.upper()}: {count}/{total_blocks}')




