from django.db import models

LOCALES = (
    ('zh', '中文'),
    ('en', 'English'),
    ('de', 'Deutsch'),
    ('nl', 'Nederlands'),
    ('fr', 'Français'),
)

class TimeStampedModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    class Meta:
        abstract = True

class Category(TimeStampedModel):
    slug = models.SlugField(unique=True)
    icon = models.ImageField(upload_to='category_icons/', blank=True, null=True)
    sort_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    def __str__(self):
        return self.slug

class CategoryTranslation(models.Model):
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, default='')
    class Meta:
        unique_together = ('category', 'locale')
    def __str__(self):
        return f"{self.category.slug} [{self.locale}]"

class Product(TimeStampedModel):
    USAGE_CHOICES = (('indoor','Indoor'),('outdoor','Outdoor'))
    categories = models.ManyToManyField(Category, related_name='products')
    cover_image = models.ImageField(upload_to='product_covers/')
    hover_image = models.ImageField(upload_to='product_covers/', blank=True, null=True, help_text='悬停时显示的第二张图片（可选）')
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    usage = models.CharField(max_length=20, choices=USAGE_CHOICES, default='indoor')
    tag_new = models.BooleanField(default=False)
    tag_hot = models.BooleanField(default=False)
    tag_featured = models.BooleanField(default=False)

    def get_chinese_name(self):
        """获取中文名称"""
        trans = self.translations.filter(locale='zh').first()
        return trans.name if trans else f"Product {self.id}"

    def __str__(self):
        return self.get_chinese_name()

class ProductImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='gallery')
    image = models.ImageField(upload_to='product_gallery/')
    sort_order = models.PositiveIntegerField(default=0)

class ProductTranslation(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    name = models.CharField(max_length=200)
    short_desc = models.CharField(max_length=300, blank=True, default='')
    rich_desc = models.TextField(blank=True, default='')
    class Meta:
        unique_together = ('product', 'locale')

class CarouselItem(TimeStampedModel):
    image = models.ImageField(upload_to='carousels/')
    link = models.URLField(blank=True, null=True)
    sort_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

class CarouselTranslation(models.Model):
    item = models.ForeignKey(CarouselItem, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    title = models.CharField(max_length=200, blank=True, default='')
    subtitle = models.CharField(max_length=300, blank=True, default='')
    cta_text = models.CharField(max_length=100, blank=True, default='')
    class Meta:
        unique_together = ('item', 'locale')



class NavigationItem(TimeStampedModel):
    TYPE = (('link','链接'),('category','分类'),('page','页面'))
    label_key = models.SlugField(help_text='用于多语言映射的 key')
    type = models.CharField(max_length=20, choices=TYPE, default='link')
    target = models.CharField(max_length=255, help_text='URL 或引用标识')
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)

class NavigationTranslation(models.Model):
    navigation = models.ForeignKey(NavigationItem, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    label = models.CharField(max_length=100)
    class Meta:
        unique_together = ('navigation', 'locale')

class ContactMessage(TimeStampedModel):
    """客户联系留言"""
    MESSAGE_TYPES = (
        ('contact', '联系我们'),
        ('newsletter', '邮件订阅'),
        ('reseller', '经销商申请'),
    )

    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='contact', verbose_name='消息类型')
    name = models.CharField(max_length=100, blank=True, default='', verbose_name='姓名')
    email = models.EmailField(verbose_name='邮箱')
    message = models.TextField(blank=True, default='', verbose_name='留言内容')
    is_read = models.BooleanField(default=False, verbose_name='已读')
    replied_at = models.DateTimeField(blank=True, null=True, verbose_name='回复时间')

    class Meta:
        verbose_name = '客户留言'
        verbose_name_plural = '客户留言'
        ordering = ['-created_at']

    def __str__(self):
        type_display = self.get_message_type_display()
        if self.name:
            return f'[{type_display}] {self.name} - {self.email} ({self.created_at.strftime("%Y-%m-%d %H:%M")})'
        else:
            return f'[{type_display}] {self.email} ({self.created_at.strftime("%Y-%m-%d %H:%M")})'

class HomeLayoutBlock(TimeStampedModel):
    BLOCK_TYPES = (
        ('carousel','轮播'),
        ('about_us','关于我们'),
        ('categories','分类'),
        ('products','产品'),
        ('reseller','成为经销商'),
        ('custom','自定义'),
    )
    block_type = models.CharField(max_length=30, choices=BLOCK_TYPES)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    config = models.JSONField(default=dict, blank=True)


class HomeLayoutBlockTranslation(models.Model):
    block = models.ForeignKey(HomeLayoutBlock, on_delete=models.CASCADE, related_name='translations')
    locale = models.CharField(max_length=5, choices=LOCALES)
    title = models.CharField(max_length=200, blank=True, default='')
    content = models.TextField(blank=True, default='')
    class Meta:
        unique_together = ('block', 'locale')


class StaticPage(TimeStampedModel):
    """静态页面管理"""
    PAGE_TYPES = (
        ('about', '关于我们'),
        ('privacy', '隐私政策'),
        ('terms', '服务条款'),
        ('contact', '联系我们'),
        ('custom', '自定义页面'),
    )
    page_type = models.CharField(max_length=20, choices=PAGE_TYPES, unique=True, verbose_name='页面类型')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    meta_title = models.CharField(max_length=200, blank=True, default='', verbose_name='页面标题')
    meta_description = models.TextField(blank=True, default='', verbose_name='页面描述')
    
    class Meta:
        verbose_name = '静态页面'
        verbose_name_plural = '静态页面'
        ordering = ['page_type']
    
    def __str__(self):
        return self.get_page_type_display()

class StaticPageTranslation(models.Model):
    """静态页面翻译"""
    page = models.ForeignKey(StaticPage, on_delete=models.CASCADE, related_name='translations', verbose_name='页面')
    locale = models.CharField(max_length=5, choices=LOCALES, verbose_name='语言')
    title = models.CharField(max_length=200, verbose_name='页面标题')
    content = models.TextField(verbose_name='页面内容')
    meta_title = models.CharField(max_length=200, blank=True, default='', verbose_name='SEO标题')
    meta_description = models.TextField(blank=True, default='', verbose_name='SEO描述')
    
    class Meta:
        unique_together = ('page', 'locale')
        verbose_name = '页面翻译'
        verbose_name_plural = '页面翻译'
    
    def __str__(self):
        return f"{self.page.get_page_type_display()} [{self.locale}]"

