# Generated by Django 4.2.13 on 2025-08-12 01:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0003_remove_product_category_product_categories'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100, verbose_name='姓名')),
                ('email', models.EmailField(max_length=254, verbose_name='邮箱')),
                ('message', models.TextField(verbose_name='留言内容')),
                ('is_read', models.BooleanField(default=False, verbose_name='已读')),
                ('replied_at', models.DateTimeField(blank=True, null=True, verbose_name='回复时间')),
            ],
            options={
                'verbose_name': '客户留言',
                'verbose_name_plural': '客户留言',
                'ordering': ['-created_at'],
            },
        ),
    ]
