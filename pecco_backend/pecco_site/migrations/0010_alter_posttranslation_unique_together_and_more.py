# Generated by Django 4.2.13 on 2025-08-14 11:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0009_staticpage_staticpagetranslation'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='posttranslation',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='posttranslation',
            name='post',
        ),
        migrations.AlterUniqueTogether(
            name='testimonialtranslation',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='testimonialtranslation',
            name='testimonial',
        ),
        migrations.AlterField(
            model_name='homelayoutblock',
            name='block_type',
            field=models.CharField(choices=[('carousel', '轮播'), ('about_us', '关于我们'), ('categories', '分类'), ('products', '产品'), ('reseller', '成为经销商'), ('custom', '自定义')], max_length=30),
        ),
        migrations.DeleteModel(
            name='Post',
        ),
        migrations.DeleteModel(
            name='PostTranslation',
        ),
        migrations.DeleteModel(
            name='Testimonial',
        ),
        migrations.DeleteModel(
            name='TestimonialTranslation',
        ),
    ]
