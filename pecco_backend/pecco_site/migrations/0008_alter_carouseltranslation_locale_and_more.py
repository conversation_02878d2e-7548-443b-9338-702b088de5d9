# Generated by Django 4.2.13 on 2025-08-13 01:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0007_alter_homelayoutblocktranslation_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='carouseltranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
        migrations.AlterField(
            model_name='categorytranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
        migrations.AlterField(
            model_name='homelayoutblocktranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
        migrations.AlterField(
            model_name='navigationtranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
        migrations.AlterField(
            model_name='posttranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
        migrations.AlterField(
            model_name='producttranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
        migrations.AlterField(
            model_name='testimonialtranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5),
        ),
    ]
