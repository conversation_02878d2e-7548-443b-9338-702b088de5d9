# Generated by Django 4.2.13 on 2025-08-13 01:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0006_alter_carouseltranslation_locale_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='homelayoutblocktranslation',
            options={},
        ),
        migrations.RemoveField(
            model_name='homelayoutblocktranslation',
            name='display_name',
        ),
        migrations.AddField(
            model_name='homelayoutblocktranslation',
            name='content',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AddField(
            model_name='homelayoutblocktranslation',
            name='title',
            field=models.CharField(blank=True, default='', max_length=200),
        ),
        migrations.AlterField(
            model_name='carouseltranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='categorytranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
        migrations.AlterField(
            model_name='homelayoutblocktranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
        migrations.AlterField(
            model_name='navigationtranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
        migrations.AlterField(
            model_name='posttranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
        migrations.AlterField(
            model_name='producttranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
        migrations.AlterField(
            model_name='testimonialtranslation',
            name='locale',
            field=models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5),
        ),
    ]
