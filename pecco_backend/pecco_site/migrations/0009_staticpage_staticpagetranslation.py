# Generated by Django 4.2.13 on 2025-08-13 03:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0008_alter_carouseltranslation_locale_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaticPage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('page_type', models.CharField(choices=[('about', '关于我们'), ('privacy', '隐私政策'), ('terms', '服务条款'), ('contact', '联系我们'), ('custom', '自定义页面')], max_length=20, unique=True, verbose_name='页面类型')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('meta_title', models.CharField(blank=True, default='', max_length=200, verbose_name='页面标题')),
                ('meta_description', models.TextField(blank=True, default='', verbose_name='页面描述')),
            ],
            options={
                'verbose_name': '静态页面',
                'verbose_name_plural': '静态页面',
                'ordering': ['page_type'],
            },
        ),
        migrations.CreateModel(
            name='StaticPageTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English'), ('de', 'Deutsch'), ('nl', 'Nederlands'), ('fr', 'Français')], max_length=5, verbose_name='语言')),
                ('title', models.CharField(max_length=200, verbose_name='页面标题')),
                ('content', models.TextField(verbose_name='页面内容')),
                ('meta_title', models.CharField(blank=True, default='', max_length=200, verbose_name='SEO标题')),
                ('meta_description', models.TextField(blank=True, default='', verbose_name='SEO描述')),
                ('page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.staticpage', verbose_name='页面')),
            ],
            options={
                'verbose_name': '页面翻译',
                'verbose_name_plural': '页面翻译',
                'unique_together': {('page', 'locale')},
            },
        ),
    ]
