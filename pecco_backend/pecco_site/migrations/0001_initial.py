# Generated by Django 4.2.13 on 2025-08-11 08:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CarouselItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('image', models.ImageField(upload_to='carousels/')),
                ('link', models.URLField(blank=True, null=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('slug', models.SlugField(unique=True)),
                ('icon', models.ImageField(blank=True, null=True, upload_to='category_icons/')),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='HomeLayoutBlock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('block_type', models.CharField(choices=[('carousel', '轮播'), ('categories', '分类'), ('products', '产品'), ('story', '品牌故事'), ('testimonials', '评价'), ('custom', '自定义')], max_length=30)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('config', models.JSONField(blank=True, default=dict)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NavigationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('label_key', models.SlugField(help_text='用于多语言映射的 key')),
                ('type', models.CharField(choices=[('link', '链接'), ('category', '分类'), ('page', '页面')], default='link', max_length=20)),
                ('target', models.CharField(help_text='URL 或引用标识', max_length=255)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to='posts/')),
                ('type', models.CharField(choices=[('story', '故事'), ('news', '资讯')], default='story', max_length=20)),
                ('status', models.CharField(choices=[('draft', '草稿'), ('published', '发布')], default='draft', max_length=20)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cover_image', models.ImageField(upload_to='product_covers/')),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('tag_new', models.BooleanField(default=False)),
                ('tag_hot', models.BooleanField(default=False)),
                ('tag_featured', models.BooleanField(default=False)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='pecco_site.category')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='testimonials/')),
                ('rating', models.PositiveSmallIntegerField(default=5)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='product_gallery/')),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gallery', to='pecco_site.product')),
            ],
        ),
        migrations.CreateModel(
            name='TestimonialTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5)),
                ('author_name', models.CharField(max_length=100)),
                ('content', models.TextField()),
                ('testimonial', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.testimonial')),
            ],
            options={
                'unique_together': {('testimonial', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='ProductTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5)),
                ('name', models.CharField(max_length=200)),
                ('short_desc', models.CharField(blank=True, default='', max_length=300)),
                ('rich_desc', models.TextField(blank=True, default='')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.product')),
            ],
            options={
                'unique_together': {('product', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='PostTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.post')),
            ],
            options={
                'unique_together': {('post', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='NavigationTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5)),
                ('label', models.CharField(max_length=100)),
                ('navigation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.navigationitem')),
            ],
            options={
                'unique_together': {('navigation', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='CategoryTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, default='')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.category')),
            ],
            options={
                'unique_together': {('category', 'locale')},
            },
        ),
        migrations.CreateModel(
            name='CarouselTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('zh', '中文'), ('en', 'English')], max_length=5)),
                ('title', models.CharField(blank=True, default='', max_length=200)),
                ('subtitle', models.CharField(blank=True, default='', max_length=300)),
                ('cta_text', models.CharField(blank=True, default='', max_length=100)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.carouselitem')),
            ],
            options={
                'unique_together': {('item', 'locale')},
            },
        ),
    ]
