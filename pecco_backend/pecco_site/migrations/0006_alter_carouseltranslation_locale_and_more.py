# Generated by Django 4.2.13 on 2025-08-13 01:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pecco_site', '0005_alter_homelayoutblock_block_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='carouseltranslation',
            name='locale',
            field=models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5),
        ),
        migrations.AlterField(
            model_name='categorytranslation',
            name='locale',
            field=models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5),
        ),
        migrations.Alter<PERSON>ield(
            model_name='navigationtranslation',
            name='locale',
            field=models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5),
        ),
        migrations.AlterField(
            model_name='posttranslation',
            name='locale',
            field=models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5),
        ),
        migrations.AlterField(
            model_name='producttranslation',
            name='locale',
            field=models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5),
        ),
        migrations.AlterField(
            model_name='testimonialtranslation',
            name='locale',
            field=models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5),
        ),
        migrations.CreateModel(
            name='HomeLayoutBlockTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('locale', models.CharField(choices=[('en', 'English'), ('zh', '中文'), ('nl', 'Nederlands'), ('fr', 'Français'), ('de', 'Deutsch')], max_length=5)),
                ('display_name', models.CharField(help_text='该语言下的显示名称', max_length=100)),
                ('block', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='pecco_site.homelayoutblock')),
            ],
            options={
                'verbose_name': '首页布局块翻译',
                'verbose_name_plural': '首页布局块翻译',
                'unique_together': {('block', 'locale')},
            },
        ),
    ]
