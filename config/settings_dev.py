from config.settings_base import *

# 开发环境特定设置
DEBUG = True
ALLOWED_HOSTS = ['*', 'localhost', '127.0.0.1']

# 开发环境可以添加一些调试工具
# 注意：需要安装 django-debug-toolbar
# pip install django-debug-toolbar
if DEBUG:
    try:
        import debug_toolbar
        INSTALLED_APPS += [
            'debug_toolbar',
        ]
        
        MIDDLEWARE += [
            'debug_toolbar.middleware.DebugToolbarMiddleware',
        ]
        
        INTERNAL_IPS = [
            '127.0.0.1',
            'localhost',
        ]
    except ImportError:
        # 如果没有安装 debug_toolbar，跳过
        pass

# 开发环境日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}
