# 环境配置解决方案

## 问题解决

### 1. settings.py 文件管理

**问题**：`settings.py` 会被提交到 Git，包含数据库信息，但有时需要修改

**解决方案**：
- 将 `settings.py` 拆分为多个文件：
  - `settings_base.py` - 基础配置（通用）
  - `settings_dev.py` - 开发环境特定配置
  - `settings_prod.py` - 生产环境特定配置
  - `settings.py` - 主文件（根据环境变量选择配置）

### 2. 环境变量管理

**问题**：开发和生产环境的配置不同

**解决方案**：
- 创建环境特定的变量文件：
  - `config/env.dev` - 开发环境变量
  - `config/env.prod` - 生产环境变量
- 使用 `.gitignore` 排除敏感文件
- 提供 `env.example` 作为模板

## 使用方法

### 开发环境

```bash
# 1. 切换到开发环境
python3 manage_env.py dev

# 2. 启动开发服务器
docker-compose up -d --build

# 3. 查看当前环境
python3 manage_env.py status
```

### 生产环境

```bash
# 1. 在服务器上配置生产环境变量
cp env.prod.example env.prod
nano env.prod  # 编辑配置

# 2. 部署
./deploy.sh
```

### 环境切换

```bash
# 查看当前环境
python3 manage_env.py status

# 切换到开发环境
python3 manage_env.py dev

# 切换到生产环境
python3 manage_env.py prod
```

## 文件结构

```
pecco-pet-shop/
├── config/                 # 配置目录
│   ├── env.dev             # 开发环境变量（不提交）
│   ├── env.prod            # 生产环境变量（不提交）
│   ├── env.example         # 环境变量示例
│   ├── settings_base.py    # 基础配置
│   ├── settings_dev.py     # 开发环境配置
│   └── settings_prod.py    # 生产环境配置
├── pecco_backend/
│   └── pecco_backend/
│       └── settings.py     # 主设置文件（选择环境）
├── .env                    # 当前环境变量（自动生成）
├── manage_env.py           # 环境管理脚本
└── deploy.sh               # 部署脚本
```

## 优势

1. **安全性**：敏感配置不提交到 Git
2. **灵活性**：可以轻松切换环境
3. **可维护性**：配置分离，易于管理
4. **自动化**：一键部署和切换环境
5. **版本控制**：代码和配置分离

## 注意事项

1. **首次使用**：需要手动创建环境变量文件
2. **Git 提交**：确保 `.gitignore` 正确配置
3. **环境切换**：切换环境后需要重启服务
4. **备份**：生产环境配置要妥善保管
