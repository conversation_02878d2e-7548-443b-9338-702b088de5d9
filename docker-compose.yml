version: '3.8'

services:
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: pecco
      MYSQL_ROOT_PASSWORD: test123356
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  web:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - DJANGO_ENV=dev
      - MYSQL_HOST=db
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=pecco
      - MYSQL_USER=root
      - MYSQL_PASSWORD=test123356
    volumes:
      - ./pecco_backend/media:/app/pecco_backend/media
      - ./pecco_backend/static:/app/pecco_backend/static
    restart: unless-stopped

volumes:
  mysql_data:
