#!/usr/bin/env python3
"""
环境管理脚本
用于在开发和生产环境之间切换
"""

import os
import shutil
import sys
from pathlib import Path

def copy_env_file(env_type):
    """复制环境变量文件"""
    # 源文件在配置目录
    source_file = Path(f"config/env.{env_type}")
    # 目标文件在项目根目录
    target_file = Path(".env")
    
    if not source_file.exists():
        print(f"错误: 找不到环境文件 {source_file}")
        return False
    
    shutil.copy2(source_file, target_file)
    print(f"✅ 已切换到 {env_type} 环境")
    print(f"   源文件: {source_file}")
    print(f"   目标文件: {target_file}")
    return True

def show_current_env():
    """显示当前环境"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if line.startswith('DJANGO_ENV='):
                    env = line.strip().split('=')[1]
                    print(f"当前环境: {env}")
                    print(f"环境文件: {env_file}")
                    return
    print("当前环境: 未设置")
    print("环境文件: 未找到")

def main():
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python manage_env.py dev     # 切换到开发环境")
        print("  python manage_env.py prod    # 切换到生产环境")
        print("  python manage_env.py status  # 查看当前环境")
        return
    
    command = sys.argv[1].lower()
    
    if command == "dev":
        copy_env_file("dev")
    elif command == "prod":
        copy_env_file("prod")
    elif command == "status":
        show_current_env()
    else:
        print(f"未知命令: {command}")
        print("可用命令: dev, prod, status")

if __name__ == "__main__":
    main()
